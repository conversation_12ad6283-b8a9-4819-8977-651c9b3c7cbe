Base on how I code @/home/<USER>/english_game/app/components/assessments/AnswerTheQuestion/ , help me code AssessmentWordOrder, it should be has enough component and logic.

In game item, Layout is word pool, and can be toggle to fill to ordering, design style should fit with current .

docs about AssessmentWordOrder already implement in backend , curl:

Data for GameItem

```
http://localhost:8000/api/public/units/52

{
    "success": true,
    "message": "Unit retrieved successfully",
    "data": {
        "id": 52,
        "course_id": 2,
        "title": "Elementary Grammar - Word Order",
        "description": "Practice elementary grammar structures through word ordering exercises.",
        "skill_type": "grammar",
        "difficulty": "elementary",
        "unit_type": "word_order",
        "unit_order": 14,
        "created_at": "2025-10-05T12:33:55.000000Z",
        "updated_at": "2025-10-05T12:33:55.000000Z",
        "course": {
            "id": 2,
            "title": "Intermediate English",
            "description": "Build on your basic English knowledge with more complex grammar structures, expanded vocabulary, and improved listening and reading skills.",
            "created_at": "2025-08-08T17:14:42.000000Z",
            "updated_at": "2025-08-08T17:14:42.000000Z"
        },
        "assessments": [
            {
                "id": 16,
                "created_at": "2025-10-05T12:33:55.000000Z",
                "updated_at": "2025-10-05T12:33:55.000000Z",
                "assessment_order": 1,
                "assessment_id": 60,
                "shuffled_words": [
                    "the",
                    "friends.",
                    "to",
                    "I",
                    "and",
                    "went",
                    "my",
                    "park",
                    "Yesterday",
                    "met"
                ],
                "files": []
            },
            {
                "id": 17,
                "created_at": "2025-10-05T12:33:55.000000Z",
                "updated_at": "2025-10-05T12:33:55.000000Z",
                "assessment_order": 2,
                "assessment_id": 61,
                "shuffled_words": [
                    "every",
                    "plays",
                    "football",
                    "school.",
                    "after",
                    "afternoon",
                    "brother",
                    "My"
                ],
                "files": []
            },
            {
                "id": 18,
                "created_at": "2025-10-05T12:33:55.000000Z",
                "updated_at": "2025-10-05T12:33:55.000000Z",
                "assessment_order": 3,
                "assessment_id": 62,
                "shuffled_words": [
                    "She",
                    "languages",
                    "three",
                    "can",
                    "well.",
                    "speak",
                    "very"
                ],
                "files": []
            }
        ]
    }
}
```

Data for score function:

````json
{
    "success": true,
    "message": "Assessment scored successfully",
    "data": {
        "explain": "Modal verb structure: Subject + modal verb + base verb + object + adverb.",
        "pass": false,
        "meta": {
            "correct_answer": "She can speak three languages very well.",
            "user_answer": "they live in london since 2010",
            "incorrect_word_indexes": [
                0,
                1,
                2,
                3,
                4,
                5,
                6
            ],
            "word_analysis": [
                {
                    "index": 0,
                    "user_word": "they",
                    "expected_word": "she",
                    "issue": "incorrect_word"
                },
                {
                    "index": 1,
                    "user_word": "live",
                    "expected_word": "can",
                    "issue": "incorrect_word"
                },
                {
                    "index": 2,
                    "user_word": "in",
                    "expected_word": "speak",
                    "issue": "incorrect_word"
                },
                {
                    "index": 3,
                    "user_word": "london",
                    "expected_word": "three",
                    "issue": "incorrect_word"
                },
                {
                    "index": 4,
                    "user_word": "since",
                    "expected_word": "languages",
                    "issue": "incorrect_word"
                },
                {
                    "index": 5,
                    "user_word": "2010",
                    "expected_word": "very",
                    "issue": "incorrect_word"
                },
                {
                    "index": 6,
                    "user_word": null,
                    "expected_word": "well",
                    "issue": "missing_word"
                }
            ]
        }
    }
}

```bash
curl --location 'http://localhost:8000/api/public/assessments/word-order/score' \
--header 'Content-Type: application/json' \
--data '{
  "assessment_id": 1,
  "answer": "They live in London since 2010"
}'

{
    "success": true,
    "message": "Assessment scored successfully",
    "data": {
        "explain": "Modal verb structure: Subject + modal verb + base verb + object + adverb.",
        "pass": false,
        "meta": {
            "correct_answer": "She can speak three languages very well.",
            "user_answer": "they live in london since 2010",
            "incorrect_word_indexes": [
                0,
                1,
                2,
                3,
                4,
                5,
                6
            ],
            "word_analysis": [
                {
                    "index": 0,
                    "user_word": "they",
                    "expected_word": "she",
                    "issue": "incorrect_word"
                },
                {
                    "index": 1,
                    "user_word": "live",
                    "expected_word": "can",
                    "issue": "incorrect_word"
                },
                {
                    "index": 2,
                    "user_word": "in",
                    "expected_word": "speak",
                    "issue": "incorrect_word"
                },
                {
                    "index": 3,
                    "user_word": "london",
                    "expected_word": "three",
                    "issue": "incorrect_word"
                },
                {
                    "index": 4,
                    "user_word": "since",
                    "expected_word": "languages",
                    "issue": "incorrect_word"
                },
                {
                    "index": 5,
                    "user_word": "2010",
                    "expected_word": "very",
                    "issue": "incorrect_word"
                },
                {
                    "index": 6,
                    "user_word": null,
                    "expected_word": "well",
                    "issue": "missing_word"
                }
            ]
        }
    }
}
````

Data for create, it will use to make dialog for admin manual create

```bash
curl --location 'http://localhost:8000/api/admin/assessments' \
--header 'Authorization: Bearer YOUR_ADMIN_TOKEN' \
--header 'Content-Type: application/json' \
--data '{
  "type": "word-order",
  "answer": "The quick brown fox jumps over the lazy dog",
  "explanation": "Classic English pangram sentence",
  "unit_attachments": [5]"
}'
```

## 2. it will use to use for edit dialog when admin manual Update Assessment (PUT)

```bash
curl --location 'http://localhost:8000/api/admin/assessments/word-order/1' \
--header 'Authorization: Bearer YOUR_ADMIN_TOKEN' \
--header 'Content-Type: application/json' \
--data '{
  "answer": "They have been living in London since 2010",
  "words": "They|have|been|living|in|London|since|2010",
  "explanation": "Present perfect continuous for life experiences"
}'
```

## 3. Delete Assessment (DELETE)

```bash
curl --location --request DELETE 'http://localhost:8000/api/admin/assessments/word-order/1' \
--header 'Authorization: Bearer YOUR_ADMIN_TOKEN'
```
