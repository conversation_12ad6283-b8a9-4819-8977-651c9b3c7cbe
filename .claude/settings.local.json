{"permissions": {"allow": ["Bash(pnpm dlx:*)", "Bash(pnpm run:*)", "WebFetch(domain:reka-ui.com)", "Bash(grep:*)", "Bash(pnpm typecheck:*)", "Bash(npx eslint:*)", "Bash(find:*)", "Bash(npx nuxt typecheck)", "Bash(npx nuxt typecheck:*)", "Bash(ls:*)", "Bash(rm:*)", "Bash(pnpm eslint:*)", "mcp__ide__executeCode", "Bash(context7 resolve-library-id:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__ide__getDiagnostics", "mcp__sequential-thinking__sequentialthinking", "mcp__serena__find_symbol", "mcp__serena__activate_project", "mcp__serena__initial_instructions", "mcp__serena__check_onboarding_performed", "mcp__serena__onboarding", "mcp__serena__list_dir", "mcp__serena__write_memory", "mcp__serena__search_for_pattern", "mcp__serena__replace_regex", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__read_memory", "mcp__serena__summarize_changes", "mcp__serena__find_file", "mcp__serena__replace_symbol_body", "mcp__serena__think_about_collected_information", "mcp__serena__get_current_config", "mcp__serena__get_symbols_overview", "Bash(npx tsc:*)", "Bash(pnpm build:*)", "<PERSON><PERSON>(timeout 10 pnpm dev)", "<PERSON><PERSON>(timeout 10 npx nuxt typecheck)", "mcp__serena__find_referencing_symbols", "Bash(git checkout:*)", "Bash(git add:*)", "Bash(git commit:*)"], "deny": []}, "enableAllProjectMcpServers": true, "enabledMcpjsonServers": ["sequential-thinking", "Context7"]}