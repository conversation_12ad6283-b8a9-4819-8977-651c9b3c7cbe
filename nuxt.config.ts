import process from 'node:process'
import tailwindcss from '@tailwindcss/vite'

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },
  modules: ['@nuxt/eslint', 'shadcn-nuxt', '@pinia/nuxt'],
  css: ['~/assets/css/tailwind.css'],
  srcDir: 'app/',
  ssr: false,
  vite: {
    plugins: [
      tailwindcss(),
    ],
    server: {
      allowedHosts: true,
    },
  },
  shadcn: {
    /**
     * Prefix for all the imported component
     */
    prefix: '',
    /**
     * Directory that the component lives in.
     * @default "./components/ui"
     */
    componentDir: './app/components/ui',
  },
  runtimeConfig: {
    // Private keys (only available on the server-side)
    apiSecret: process.env.API_SECRET || '',

    // Public keys (exposed to the client-side)
    public: {
      apiBaseUrl: process.env.API_BASE_URL,
      appUrl: process.env.APP_URL,
    },
  },
})
