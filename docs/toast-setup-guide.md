# Toast Setup Guide

## Quick Setup (Already Done)

This guide documents the setup process that has already been completed in your project.

## Installation Steps Completed

### 1. ✅ Installed Dependencies

```bash
# Sonner component via shadcn-vue CLI
npx shadcn-vue@latest add sonner

# Vue-sonner library
pnpm add vue-sonner
```

### 2. ✅ Added CSS Import

Added to `app/assets/css/tailwind.css`:

```css
@import 'vue-sonner/style.css';
```

### 3. ✅ Added Toaster to App

Added to `app/app.vue`:

```vue
<script setup lang="ts">
import { Toaster } from '@/components/ui/sonner'
</script>

<template>
  <div>
    <NuxtRouteAnnouncer />
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    <GlobalModal />
    <Toaster />
  </div>
</template>
```

### 4. ✅ Created Composables

Created the following composables in `app/composables/`:

- `useToast.ts` - Basic toast functionality
- `useGameToasts.ts` - Game-specific notifications
- `useAssessmentWithToasts.ts` - Assessment operations with toast feedback

### 5. ✅ Created Demo Components

- `app/components/ToastDemo.vue` - Basic toast examples
- `app/components/GameToastExamples.vue` - Game-specific examples
- `app/pages/toast-demo.vue` - Demo page at `/toast-demo`

## File Structure

```
app/
├── assets/css/
│   └── tailwind.css              # Added vue-sonner CSS import
├── components/
│   ├── ui/sonner/
│   │   ├── Sonner.vue           # Toaster component
│   │   └── index.ts             # Export
│   ├── ToastDemo.vue            # Basic demo component
│   └── GameToastExamples.vue    # Game-specific demo
├── composables/
│   ├── useToast.ts              # Basic toast composable
│   ├── useGameToasts.ts         # Game-specific toasts
│   └── useAssessmentWithToasts.ts # Assessment operations
├── pages/
│   └── toast-demo.vue           # Demo page
└── app.vue                      # Added Toaster component

docs/
├── toast-notifications.md       # Main documentation
├── toast-api-reference.md       # API reference
└── toast-setup-guide.md        # This setup guide
```

## Configuration Options

### Toaster Configuration

You can customize the Toaster in `app.vue`:

```vue
<template>
  <Toaster
    position="top-right"
    :duration="4000"
    :visible-toasts="5"
    :close-button="true"
    theme="system"
    :expand="true"
    rich-colors
  />
</template>
```

### Available Positions

- `top-left`
- `top-center`
- `top-right` (default)
- `bottom-left`
- `bottom-center`
- `bottom-right`

### Theme Options

- `light` - Always light theme
- `dark` - Always dark theme
- `system` - Follow system preference (default)

## Customization

### Custom Styling

The toasts automatically inherit your shadcn/ui theme. To customize further, you can override CSS variables:

```css
:root {
  --normal-bg: var(--popover);
  --normal-text: var(--popover-foreground);
  --normal-border: var(--border);
}
```

### Custom Toast Types

Create your own toast variants by extending the base composable:

```typescript
// composables/useCustomToasts.ts
export function useCustomToasts() {
  const { toast } = useToast()

  const celebration = (message: string) => {
    toast(`🎉 ${message}`, {
      description: 'Amazing work!',
      duration: 5000
    })
  }

  return { celebration }
}
```

## Integration Examples

### In a Vue Component

```vue
<script setup lang="ts">
// Import what you need
const { success, error } = useToast()
const gameToasts = useGameToasts()

// Use in methods
function handleLevelComplete() {
  gameToasts.levelComplete(currentLevel.value, pointsEarned.value)
}

async function handleSave() {
  try {
    await saveData()
    success('Data saved successfully!')
  }
  catch (err) {
    error('Failed to save data', {
      description: err.message
    })
  }
}
</script>

<template>
  <div>
    <Button @click="handleLevelComplete">
      Complete Level
    </Button>
    <Button @click="handleSave">
      Save Progress
    </Button>
  </div>
</template>
```

### In a Composable

```typescript
// composables/useGameLogic.ts
export function useGameLogic() {
  const gameToasts = useGameToasts()

  const completeLevel = (level: number, score: number) => {
    // Game logic here...

    // Show toast notification
    gameToasts.levelComplete(level, score)

    // Check for achievements
    if (score === 100) {
      gameToasts.perfectScore()
    }
  }

  return { completeLevel }
}
```

### With API Calls

```typescript
// repositories/enhanced-assessment.ts
export const enhancedAssessmentRepository = {
  async createAssessment(data: CreateAssessmentData) {
    const { promise } = useToast()

    const createPromise = assessmentRepository.createAssessment(data)

    return promise(createPromise, {
      loading: 'Creating assessment...',
      success: result => `Assessment "${result.title}" created!`,
      error: err => `Failed to create: ${err.message}`
    })
  }
}
```

## Testing

### Manual Testing

Visit `/toast-demo` to test all toast functionality:

1. Basic toast types (success, error, warning, info)
2. Loading and promise toasts
3. Game-specific notifications
4. Interactive toasts with actions
5. Assessment management toasts

### Automated Testing

For unit tests, you can mock the toast functions:

```typescript
// tests/setup.ts
vi.mock('@/composables/useToast', () => ({
  useToast: () => ({
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
    toast: vi.fn(),
    loading: vi.fn(),
    promise: vi.fn(),
    dismiss: vi.fn()
  })
}))
```

## Troubleshooting

### Common Issues

1. **Toasts not appearing**
   - Check that `<Toaster />` is in `app.vue`
   - Verify CSS import in `tailwind.css`
   - Check browser console for errors

2. **Styling issues**
   - Ensure shadcn/ui theme is properly configured
   - Check CSS variable definitions
   - Verify Tailwind CSS is working

3. **TypeScript errors**
   - Check that all types are properly imported
   - Verify composable return types match usage
   - Ensure vue-sonner types are available

### Debug Mode

Enable debug logging by adding to your composable:

```typescript
const { toast } = useToast()

// Debug wrapper
function debugToast(message: string, options?: any) {
  console.log('Toast:', message, options)
  return toast(message, options)
}
```

## Performance Tips

1. **Avoid toast spam** - Don't show too many toasts at once
2. **Use appropriate durations** - Longer for errors, shorter for success
3. **Clean up loading toasts** - Always dismiss loading toasts when done
4. **Batch related notifications** - Stagger multiple related toasts

## Next Steps

1. **Integrate into existing components** - Replace alert() calls with toasts
2. **Add to error boundaries** - Show user-friendly error toasts
3. **Enhance API error handling** - Use toast feedback for all API calls
4. **Add analytics** - Track toast interactions for UX insights

## Support

- **Documentation**: See `docs/toast-notifications.md` for detailed usage
- **API Reference**: See `docs/toast-api-reference.md` for complete API
- **Demo**: Visit `/toast-demo` for interactive examples
- **Issues**: Check browser console and network tab for debugging
