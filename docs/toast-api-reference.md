# Toast API Reference

## useToast()

The main composable for basic toast functionality.

### Return Value

```typescript
interface UseToastReturn {
  toast: (message: string, options?: ToastOptions) => string | number
  success: (message: string, options?: Omit<ToastOptions, 'title'>) => string | number
  error: (message: string, options?: Omit<ToastOptions, 'title'>) => string | number
  warning: (message: string, options?: Omit<ToastOptions, 'title'>) => string | number
  info: (message: string, options?: Omit<ToastOptions, 'title'>) => string | number
  loading: (message: string, options?: Omit<ToastOptions, 'title'>) => string | number
  promise: <T>(promise: Promise<T>, messages: PromiseMessages<T>, options?: ToastOptions) => Promise<T>
  dismiss: (toastId?: string | number) => void
}
```

### ToastOptions

```typescript
interface ToastOptions {
  title?: string
  description?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}
```

### Methods

#### `toast(message, options?)`

Display a basic toast notification.

**Parameters:**

- `message` (string): Main toast message
- `options` (ToastOptions, optional): Additional configuration

**Returns:** Toast ID for dismissal

**Example:**

```typescript
const toastId = toast('Hello World!', {
  description: 'This is a basic toast',
  duration: 3000
})
```

#### `success(message, options?)`

Display a success toast (green).

**Parameters:**

- `message` (string): Success message
- `options` (Omit<ToastOptions, 'title'>, optional): Configuration without title

**Example:**

```typescript
success('Data saved successfully!', {
  description: 'Your changes have been applied',
  duration: 3000
})
```

#### `error(message, options?)`

Display an error toast (red).

**Parameters:**

- `message` (string): Error message
- `options` (Omit<ToastOptions, 'title'>, optional): Configuration without title

**Example:**

```typescript
error('Failed to save data', {
  description: 'Please check your connection and try again',
  duration: 5000
})
```

#### `warning(message, options?)`

Display a warning toast (yellow).

**Parameters:**

- `message` (string): Warning message
- `options` (Omit<ToastOptions, 'title'>, optional): Configuration without title

#### `info(message, options?)`

Display an info toast (blue).

**Parameters:**

- `message` (string): Info message
- `options` (Omit<ToastOptions, 'title'>, optional): Configuration without title

#### `loading(message, options?)`

Display a loading toast with spinner.

**Parameters:**

- `message` (string): Loading message
- `options` (Omit<ToastOptions, 'title'>, optional): Configuration without title

**Returns:** Toast ID for dismissal

**Example:**

```typescript
const loadingId = loading('Processing...', {
  description: 'Please wait while we save your data'
})

// Later, dismiss the loading toast
dismiss(loadingId)
```

#### `promise(promise, messages, options?)`

Display a toast that follows a promise lifecycle.

**Parameters:**

- `promise` (Promise<T>): The promise to track
- `messages` (PromiseMessages<T>): Messages for each state
- `options` (ToastOptions, optional): Additional configuration

**PromiseMessages Interface:**

```typescript
interface PromiseMessages<T> {
  loading: string
  success: string | ((data: T) => string)
  error: string | ((error: any) => string)
}
```

**Example:**

```typescript
const apiCall = fetch('/api/data')

promise(apiCall, {
  loading: 'Fetching data...',
  success: data => `Loaded ${data.length} items`,
  error: err => `Failed to load: ${err.message}`
})
```

#### `dismiss(toastId?)`

Dismiss toast(s).

**Parameters:**

- `toastId` (string | number, optional): Specific toast to dismiss. If omitted, dismisses all toasts.

---

## useGameToasts()

Game-specific toast notifications with pre-configured messages and styling.

### Return Value

```typescript
interface UseGameToastsReturn {
  // Level and Progress
  levelComplete: (level: number, points: number, timeBonus?: number) => void
  courseComplete: (courseName: string, totalPoints: number) => void
  unitComplete: (unitName: string, accuracy: number) => void

  // Achievements
  achievementUnlocked: (achievementName: string, description?: string) => void
  streakAchievement: (days: number) => void
  perfectScore: (assessmentName?: string) => void

  // Assessment and Quiz
  assessmentSaved: (autoSave?: boolean) => void
  assessmentSubmitted: (score?: number, total?: number) => void
  timeWarning: (minutesLeft: number) => void
  timeAlmostUp: () => void

  // Errors and Connection
  connectionError: () => void
  saveError: () => void
  loadError: (itemType?: string) => void

  // Learning Progress
  vocabularyLearned: (wordCount: number) => void
  skillImproved: (skillName: string, improvement: string) => void
  dailyGoalReached: () => void

  // Interactive Toasts
  reviewMistakes: (mistakeCount: number, onReview: () => void) => void
  retryAssessment: (onRetry: () => void) => void
  continueToNext: (nextItemName: string, onContinue: () => void) => void

  // Utility
  customGameToast: (type: 'success' | 'error' | 'warning' | 'info', title: string, description?: string, duration?: number) => void
}
```

### Methods

#### `levelComplete(level, points, timeBonus?)`

Show level completion notification.

**Parameters:**

- `level` (number): Completed level number
- `points` (number): Points earned
- `timeBonus` (number, optional): Additional time bonus points

**Example:**

```typescript
levelComplete(5, 150, 25) // Level 5, 150 points, 25 time bonus
```

#### `achievementUnlocked(name, description?)`

Show achievement unlock notification.

**Parameters:**

- `name` (string): Achievement name
- `description` (string, optional): Achievement description

#### `assessmentSubmitted(score?, total?)`

Show assessment submission confirmation.

**Parameters:**

- `score` (number, optional): User's score
- `total` (number, optional): Total possible points

#### `timeWarning(minutesLeft)`

Show time warning for timed assessments.

**Parameters:**

- `minutesLeft` (number): Minutes remaining

#### `reviewMistakes(mistakeCount, onReview)`

Show interactive toast for reviewing mistakes.

**Parameters:**

- `mistakeCount` (number): Number of mistakes found
- `onReview` (function): Callback when review button is clicked

---

## useAssessmentWithToasts()

Enhanced assessment repository operations with automatic toast feedback.

### Return Value

```typescript
interface UseAssessmentWithToastsReturn {
  listAssessments: (query?: AssessmentListQuery) => Promise<AssessmentListResponse | null>
  createAssessment: (data: CreateAssessmentData) => Promise<Assessmentable | null>
  updateAssessment: (id: number, data: UpdateAssessmentData) => Promise<Assessmentable | null>
  deleteAssessment: (id: number, title?: string) => Promise<boolean>
  attachAssessmentToUnits: (id: number, data: AttachUnitsData) => Promise<Assessmentable | null>
  detachAssessmentFromUnits: (id: number, data: DetachUnitsData) => Promise<Assessmentable | null>
  getAssessment: (id: number) => Promise<Assessmentable | null>
  getPublicAssessment: (id: number) => Promise<Assessmentable | null>
}
```

### Methods

#### `createAssessment(data)`

Create assessment with automatic toast feedback.

**Parameters:**

- `data` (CreateAssessmentData): Assessment creation data

**Returns:** Promise<Assessmentable | null>

**Toast Behavior:**

- Shows loading toast during creation
- Shows success toast with assessment title on success
- Shows error toast with error message on failure

#### `deleteAssessment(id, title?)`

Delete assessment with confirmation-style feedback.

**Parameters:**

- `id` (number): Assessment ID
- `title` (string, optional): Assessment title for better feedback

**Returns:** Promise<boolean>

**Toast Behavior:**

- Shows loading toast with warning message
- Shows success toast confirming deletion
- Shows error toast if deletion fails

---

## Configuration

### Toaster Component Props

The `<Toaster />` component accepts these props:

```typescript
interface ToasterProps {
  position?: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'
  hotkey?: string[]
  richColors?: boolean
  expand?: boolean
  duration?: number
  gap?: number
  visibleToasts?: number
  closeButton?: boolean
  toastOptions?: ToastOptions
  className?: string
  style?: CSSProperties
  offset?: string | number
  theme?: 'light' | 'dark' | 'system'
  dir?: 'ltr' | 'rtl'
}
```

### Default Configuration

```vue
<template>
  <Toaster
    position="top-right"
    :duration="4000"
    :visible-toasts="5"
    :close-button="true"
    theme="system"
  />
</template>
```

---

## TypeScript Types

### Core Types

```typescript
// Toast options for basic toasts
interface ToastOptions {
  title?: string
  description?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

// Promise toast messages
interface PromiseMessages<T> {
  loading: string
  success: string | ((data: T) => string)
  error: string | ((error: any) => string)
}

// Toast types
type ToastType = 'success' | 'error' | 'warning' | 'info' | 'loading' | 'default'

// Toast position
type ToastPosition = 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right'
```

### Assessment Types

```typescript
// These types should match your existing assessment types
interface Assessment {
  id: number
  title: string
  description: string
  // ... other assessment properties
}

interface CreateAssessmentData {
  title: string
  description: string
  instructions: string
  time_limit?: number
  questions: any[]
}

interface UpdateAssessmentData {
  title?: string
  description?: string
  instructions?: string
  time_limit?: number
  questions?: any[]
}
```

---

## Browser Support

The toast system supports all modern browsers:

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

For older browsers, ensure you have appropriate polyfills for:

- Promise
- CSS Custom Properties
- ES6 features

---

## Performance Considerations

### Memory Management

- Toast IDs are automatically cleaned up when toasts are dismissed
- Avoid creating excessive toasts in rapid succession
- Use `dismiss()` to clean up long-running loading toasts

### Bundle Size

- vue-sonner: ~15KB gzipped
- Additional composables: ~3KB gzipped
- Total impact: ~18KB gzipped

### Accessibility

- All toasts are announced to screen readers
- Keyboard navigation supported
- Focus management handled automatically
- ARIA labels and roles properly set
