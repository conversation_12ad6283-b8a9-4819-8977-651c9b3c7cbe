# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Package Manager**: This project uses `pnpm` as the package manager.

```bash
# Install dependencies
pnpm install

# Start development server (http://localhost:3000)
pnpm dev

# Build for production
pnpm build

# Preview production build
pnpm preview

# Lint code (using @antfu/eslint-config)
pnpm eslint

# Run type checking
npx nuxt typecheck

# Generate static site
pnpm generate
```

## Architecture Overview

This is a **Nuxt 4** application with a modern TypeScript-first architecture using:

- **Framework**: Nuxt 4 with TypeScript
- **UI Components**: Shadcn/ui with reka-ui components
- **Styling**: TailwindCSS v4 with custom animations
- **Directory Structure**: Custom `srcDir: 'app/'` configuration

### Key Architecture Patterns

**API Client Pattern**: The application follows a structured API client architecture:

- All API calls use composables: `useAPI()`, `useLazyAPI()`, and `useAPICall()`
- Repository pattern with dedicated repository files in `app/repositories/`
- Type-safe API responses using interfaces in `shared/types/api.d.ts`

**Data Fetching Strategy**:

- **Pages own their data**: Pages are responsible for their own data fetching logic
- **Repository pattern**: All API calls are abstracted into repository methods
- **URL-driven state**: Query parameters are the single source of truth for bookmarkable state
- Use `useAsyncData` with repository methods for SSR-compatible data fetching
- Use `useQueryParams` for reactive URL parameter management

**Component Architecture**:

- UI components located in `app/components/ui/` (auto-imported by Nuxt)
- Shadcn/ui components are used for consistent design system
- Custom components follow Vue 3 Composition API patterns

### Directory Structure

```
app/                    # Main source directory (srcDir)
├── assets/css/         # Global styles
├── components/ui/      # Shadcn/ui components
├── composables/        # Vue composables
├── lib/               # Utility functions
├── plugins/           # Nuxt plugins
├── repositories/      # API repository pattern
└── types/            # TypeScript type definitions

shared/                # Shared utilities and types
├── constants/         # Application constants
├── types/            # Shared TypeScript types
└── utils/           # Shared utility functions

rules/                 # Development guidelines and documentation
```

## Important Development Guidelines

**Data Fetching Pattern** (from `rules/coding-style.mdc`):

- Pages should use `useAsyncData` to call repository methods directly
- Don't create page-specific composables for data fetching
- Use `watch: [queryParams]` to automatically refetch when URL changes
- Repository methods should be simple async functions that return API data

**API Client Usage**:

- Use `useAPI()` for SSR-friendly data fetching
- Use `useLazyAPI()` for non-critical data that can load after navigation
- Use `useAPICall()` for user-triggered actions (forms, buttons)
- All API responses follow a consistent `ApiResponse<T>` structure

**Component Installation**:

```bash
# Add new Shadcn/ui components
pnpm dlx shadcn-vue@latest add [component-name]
```

**Code Quality**:

- ESLint configuration uses @antfu/eslint-config with formatters enabled
- TypeScript strict mode is enabled
- Follow Vue 3 Composition API patterns

**Environment Configuration**:

- `API_BASE_URL` - Backend API endpoint
- `APP_URL` - Frontend application URL
- `API_SECRET` - Server-side API authentication secret

## Key Composables

- `useAPI()` - SSR-compatible data fetching
- `useAPICall()` - Direct API calls for actions
- `useQueryParams()` - Reactive URL parameter management
- `useBaseQuery()` - Common query operations (search, pagination)

## Type Safety

The application is fully typed with:

- API response types in `shared/types/api.d.ts`
- Application-specific types in `app/types/`
- Laravel-style pagination support with `LaravelPaginatedResponse<T>`

## Project Context

This is an **educational English learning game** platform with the following features:

- **Multi-stage gameplay**: Users progress through stages with multiple levels
- **Question-based learning**: Multiple choice questions with explanations
- **Progress tracking**: Lives system, scoring, and completion statistics
- **Admin panel**: Staff authentication and dashboard management
- **Responsive design**: Works across devices with TailwindCSS

### Game Flow Architecture

- **Stages & Levels**: `/game/stage/[stageId]/level/[levelId]` route structure
- **Question System**: Supports multiple choice with explanations
- **Progress State**: Lives, scoring, and advancement logic
- **Modals**: Level completion and game over notifications

### Key Game Components

- `QuestionCard.vue` - Main question display component
- `GameNavigation.vue` - Game flow controls
- `GlobalModal.vue` - Shared modal system for game events
- `useQuestions.ts` - Game state management composable
