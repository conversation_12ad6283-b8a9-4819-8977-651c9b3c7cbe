# Modal Usage Examples

## Simple Import-Based Approach

Just import the `useModal` composable where you need it - no global setup required!

### Basic Examples

```vue
<script setup lang="ts">
// 1. Nuxt auto-imports composables and Vue basics (ref, computed, etc.)
import GameOverModal from '@/components/modals/GameOverModal.vue'

// 2. Use the modal
const modal = useModal()

// Simple alert
async function showAlert() {
  await modal.alert({
    title: 'Success!',
    description: 'Your changes have been saved.',
    variant: 'success'
  })
  console.log('Alert closed!')
}

// Confirmation dialog
async function confirmDelete() {
  const confirmed = await modal.confirm({
    title: 'Delete Item',
    description: 'Are you sure? This cannot be undone.',
    variant: 'destructive'
  })

  if (confirmed) {
    // User confirmed - proceed with deletion
    console.log('Deleting...')
  }
}

// Custom component modal
async function showGameOver() {
  const result = await modal.custom(GameOverModal, {
    score: 1250,
    correctAnswers: 8,
    totalQuestions: 10
  })

  if (result === 'tryAgain') {
    console.log('Player wants to try again!')
  }
}
</script>

<template>
  <div>
    <Button @click="showAlert">
      Show Alert
    </Button>
    <Button @click="confirmDelete">
      Confirm Delete
    </Button>
    <Button @click="showGameOver">
      Game Over Modal
    </Button>
  </div>
</template>
```

### Available Methods

```typescript
// Basic modals
await modal.alert({ title: 'Info', description: 'Something happened' })
const confirmed = await modal.confirm({ title: 'Are you sure?' })

// Custom component modal
const result = await modal.custom(MyComponent, { prop1: 'value' })

// Simple methods
modal.open({ title: 'Custom Modal', content: '<p>HTML content</p>' })
modal.close()
modal.setLoading(true)
```

### Modal Variants

```typescript
// Different visual styles
modal.alert({ title: 'Success!', variant: 'success' }) // Green
modal.alert({ title: 'Warning!', variant: 'warning' }) // Yellow
modal.alert({ title: 'Error!', variant: 'destructive' }) // Red
modal.alert({ title: 'Info', variant: 'info' }) // Blue
```

### Modal Sizes

```typescript
modal.open({ title: 'Small', size: 'sm' }) // 384px max-width
modal.open({ title: 'Medium', size: 'md' }) // 448px (default)
modal.open({ title: 'Large', size: 'lg' }) // 512px
modal.open({ title: 'XL', size: 'xl' }) // 576px
modal.open({ title: 'Full', size: 'full' }) // Full screen
```

## How It Works

1. **Auto-Import**: Nuxt automatically imports `useModal` - no import needed!
2. **Use**: Call `const modal = useModal()`
3. **Display**: Use `modal.alert()`, `modal.confirm()`, or `modal.custom()`
4. **Wait**: All methods return promises - use `await` to wait for user interaction

## Files Structure

```
app/
├── composables/
│   └── useModal.ts              # The main composable
├── types/
│   └── modal.ts                 # TypeScript interfaces
├── components/
│   ├── GlobalModal.vue          # Main modal component (in app.vue)
│   └── modals/
│       ├── GameOverModal.vue    # Custom game over modal
│       ├── LevelCompleteModal.vue
│       └── README.md            # Full documentation
└── app.vue                      # Contains <GlobalModal />
```

## Integration

The `GlobalModal` component is included in `app.vue`, so it's available everywhere. Just import the composable and use it!

## Notes

- ✅ Nuxt 4 auto-imports composables and Vue basics (`ref`, `computed`, etc.)
- ✅ No imports needed - `useModal` is auto-imported
- ✅ Full TypeScript support
- ✅ Responsive and accessible
- ✅ Keyboard navigation (ESC, Enter)
- ✅ Custom components supported
