# Project Overview

This is an **English Learning Game** platform built with Nuxt 4 and TypeScript.

## Purpose

Educational platform featuring multi-stage gameplay where users progress through stages with multiple levels, answering questions to learn English.

## Tech Stack

- **Framework**: Nuxt 4 with TypeScript
- **UI Components**: Shadcn/ui with reka-ui components
- **Styling**: TailwindCSS v4 with custom animations
- **State Management**: Pinia with persistence
- **Package Manager**: pnpm
- **Architecture**: Custom `srcDir: 'app/'` configuration

## Key Features

- Multi-stage gameplay with questions and levels
- Admin panel for content management
- Progress tracking with lives system
- Responsive design
- SSR disabled (SPA mode)

## Architecture Patterns

- Repository pattern for API calls
- URL-driven state management
- Component-based architecture
- Type-safe API responses
