# Suggested Commands

## Development Commands

```bash
# Install dependencies
pnpm install

# Start development server (http://localhost:3000)
pnpm dev

# Build for production
pnpm build

# Preview production build
pnpm preview

# Generate static site
pnpm generate
```

## Code Quality

```bash
# Run ESLint
pnpm eslint

# Run TypeScript type checking
npx nuxt typecheck
```

## Component Management

```bash
# Add new Shadcn/ui components
pnpm dlx shadcn-vue@latest add [component-name]
```

## System Commands (Linux)

- `ls` - list directory contents
- `cd` - change directory
- `grep` - search text patterns
- `find` - find files and directories
- `git` - version control operations
