---
description: Project Coding Style and Data Fetching Guide
globs:
alwaysApply: true
---
# Project Coding Style and Data Fetching Guide

This document outlines the standard coding style and data fetching patterns for this project, using the **User Listing page** (`pages/users/index.vue`) as the canonical example. Adhering to these conventions will ensure the codebase remains clean, maintainable, and consistent.

## Core Principles

1.  **Page-Owned Logic**: Pages are the primary owners of their data-fetching logic. They orchestrate calls to repositories and manage their own state.
2.  **Lean Composables**: Composables should be reserved for truly generic and reusable logic that is not tied to a specific data type (e.g., `useQueryParams`, `useBaseQuery`). Page-specific data fetching and state management should **not** be wrapped in a composable.
3.  **Repository Pattern**: All direct API interactions must be encapsulated within a repository file (e.g., `repositories/user.ts`). Repositories are responsible for making the API calls and handling the basic response structure.
4.  **URL-Driven State**: The URL is the single source of truth for page state that needs to be bookmarkable and shareable, such as filters, search queries, and pagination. `useQueryParams` is the gateway to this state.

---

## Data Fetching Pattern

The primary data fetching pattern involves the page using `useAsyncData` to call a method from a repository. This keeps the logic self-contained within the page that needs the data.

### Step 1: Define the Repository Method

All API calls must be defined in a repository. A repository method should be a simple async function that calls the API and returns the data.

**Example: `repositories/user.ts`**
```typescript
import type { LaravelPaginatedResponse } from '~/types/api'
import type { ApiUser } from '~/types/user'

export const userRepository = {
  async getUsers(query?: string, page?: string): Promise<LaravelPaginatedResponse<ApiUser>> {
    const { get } = useAPICall()
    const params: Record<string, any> = {}

    if (query) params.q = query
    if (page) params.page = page

    // The 'get' function is from useAPICall, which handles the actual fetch
    const response = await get<LaravelPaginatedResponse<ApiUser>>('/api/admin/users', params)
    return response
  },
}
```

### Step 2: Fetch Data in the Page Component

In the page component (e.g., `pages/users/index.vue`), use `useAsyncData` to call the repository method.

-   The first argument is a unique key for the data.
-   The second argument is the async function that fetches the data.
-   The `watch` option is crucial for reactivity. By watching `queryParams`, `useAsyncData` will automatically re-run the fetcher whenever a URL parameter changes.

**Example: `pages/users/index.vue`**
```vue
<script setup lang="ts">
import { computed } from 'vue'
import { useQueryParams } from '~/composables/useQueryParams'
import { userRepository } from '~/repositories/user'

const { queryParams } = useQueryParams()

// Data fetching is done directly in the page
const { data, pending: loading, error, refresh: fetchUsers } = useAsyncData(
  'usersList',
  () => userRepository.getUsers(
    queryParams.value.q as string | undefined,
    queryParams.value.page as string | undefined,
  ),
  {
    // This makes the data automatically refresh when the URL changes
    watch: [queryParams],
  },
)

// Create computed properties to safely access the fetched data
const users = computed(() => data.value?.data || [])
const pagination = computed(() => data.value)
</script>
```

---

## UI and State Management

### Handling UI Actions (Search and Pagination)

User interactions that modify the URL state (like searching or changing pages) should use the actions provided by `useBaseQuery`.

**Example: `pages/users/index.vue`**
```vue
<script setup lang="ts">
import { useBaseQuery } from '~/composables/useBaseQuery'
import { useQueryParams } from '~/composables/useQueryParams'

const { search, nextPage, prevPage, goToPage } = useBaseQuery()
const { queryParams } = useQueryParams()

// This function is called when the user clears the search
function handleClearSearch() {
  search('') // This updates the URL, which triggers the data fetcher
}
</script>

<template>
  <!-- Search Input -->
  <Input
    :model-value="queryParams.q as string"
    @input="search"
  />

  <!-- Pagination Buttons -->
  <Button @click="prevPage">Previous</Button>
  <Button @click="goToPage(2)">Go to Page 2</Button>
  <Button @click="nextPage">Next</Button>
</template>
```
*Note: The `@input="search"` on the `Input` component directly calls the `search` function from `useBaseQuery`. This is a simplified example; for debouncing, a local `ref` and `watch` are appropriate, as seen in the actual `users/index.vue`.*

### Handling Page-Specific Logic

Any logic or utility function that is only used by a single page should be defined directly within that page's script block.

**Example: `pages/users/index.vue`**
```vue
<script setup lang="ts">
// This function is specific to how we want to display user avatars on this page
function getUserInitials(name: string): string {
  if (!name) return ''
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2)
}
</script>
```

This approach keeps page-specific logic co-located with the component that uses it, preventing the over-abstraction of composables.
