version: '3.8'

services:
  admin-dashboard:
    build: .
    ports:
      - '3005:3005'
    environment:
      - NODE_ENV=production
      - API_BASE_URL=${API_BASE_URL:-http://localhost:3001/api}
      - APP_URL=${APP_URL:-http://localhost:3000}
      - API_SECRET=${API_SECRET}
    restart: unless-stopped
    healthcheck:
      test: [CMD, wget, --no-verbose, --tries=1, --spider, 'http://localhost:3005/']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - admin-network

networks:
  admin-network:
    driver: bridge

# Production deployment with environment file
# Create a .env file with:
# API_BASE_URL=https://your-api-domain.com/api
# APP_URL=https://your-admin-domain.com
# API_SECRET=your-secret-key
