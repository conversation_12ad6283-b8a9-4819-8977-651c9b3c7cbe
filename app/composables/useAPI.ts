import type { ApiEndpoint, ApiResponse } from '~/types/api'

/**
 * Enhanced useFetch wrapper that uses our custom API client
 * Provides TypeScript safety and consistent error handling
 * Works with SSR and client-side rendering
 */
export function useAPI<DataT = any>(
  endpoint: ApiEndpoint,
  options: any = {},
) {
  const { $api } = useNuxtApp()

  // Extract custom options
  const { transform, ...fetchOptions } = options

  return useFetch(endpoint, {
    ...fetchOptions,
    $fetch: $api,
    // Transform the response to extract data
    transform: transform || ((response: ApiResponse<DataT>) => response.data),
    // Add default error handling
    onResponseError: ({ response }) => {
      // Additional error handling can be added here
      console.error('API Error:', response._data)
    },
  })
}

/**
 * Lazy version of useAPI that doesn't block navigation
 * Perfect for non-critical data that can load after the page renders
 */
export function useLazyAPI<DataT = any>(
  endpoint: ApiEndpoint,
  options: any = {},
) {
  return useAPI(endpoint, { ...options, lazy: true })
}

/**
 * Direct API call function for event-driven requests
 * Use this for form submissions, button clicks, etc.
 * Works on both client and server
 */
export function useAPICall() {
  const { $api } = useNuxtApp()

  /**
   * Make a direct API call
   */
  async function call<DataT = any>(
    endpoint: string,
    options: {
      method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'
      body?: any
      query?: Record<string, any>
      headers?: Record<string, string>
    } = {},
  ): Promise<DataT> {
    try { // eslint-disable-line no-useless-catch
      const response = await $api<ApiResponse<DataT>>(endpoint, {
        method: options.method || 'GET',
        body: options.body,
        query: options.query,
        headers: options.headers,
      })

      return response.data
    }
    catch (error: any) {
      throw error
    }
  }

  /**
   * GET request
   */
  async function get<DataT = any>(
    endpoint: string,
    query?: Record<string, any>,
  ): Promise<DataT> {
    return call<DataT>(endpoint, { method: 'GET', query })
  }

  /**
   * POST request
   */
  async function post<DataT = any>(
    endpoint: string,
    body?: any,
  ): Promise<DataT> {
    return call<DataT>(endpoint, { method: 'POST', body })
  }

  /**
   * PUT request
   */
  async function put<DataT = any>(
    endpoint: string,
    body?: any,
  ): Promise<DataT> {
    return call<DataT>(endpoint, { method: 'PUT', body })
  }

  /**
   * PATCH request
   */
  async function patch<DataT = any>(
    endpoint: string,
    body?: any,
  ): Promise<DataT> {
    return call<DataT>(endpoint, { method: 'PATCH', body })
  }

  /**
   * DELETE request
   */
  async function del<DataT = any>(
    endpoint: string,
  ): Promise<DataT> {
    return call<DataT>(endpoint, { method: 'DELETE' })
  }

  return {
    call,
    get,
    post,
    put,
    patch,
    delete: del,
  }
}

/**
 * Utility composable for handling API loading states
 */
export function useAPIState() {
  const loading = ref(false)
  const error = ref<any>(null)
  const data = ref<any>(null)

  const execute = async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
    try {
      loading.value = true
      error.value = null
      const result = await apiCall()
      data.value = result
      return result
    }
    catch (err: any) {
      error.value = err
      throw err
    }
    finally {
      loading.value = false
    }
  }

  const reset = () => {
    loading.value = false
    error.value = null
    data.value = null
  }

  return {
    loading: readonly(loading),
    error: readonly(error),
    data: readonly(data),
    execute,
    reset,
  }
}
