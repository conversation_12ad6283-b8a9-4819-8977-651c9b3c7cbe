import type { AiAssessmentItem, TestPromptRequest } from '@/repositories/aiAssessment'
import { aiAssessmentRepository } from '@/repositories/aiAssessment'

/**
 * Composable for managing AI assessment test execution
 */
export function useAiAssessmentTest() {
  const loadingTest = ref(false)

  // Submit test
  async function submitTest(
    selectedQuestion: AiAssessmentItem,
    completedSentence: string,
    onSuccess: (result: any) => void,
    onError: (error: string) => void,
  ) {
    try {
      loadingTest.value = true

      const request: TestPromptRequest = {
        unit_id: selectedQuestion.unit_id,
        answers: [{
          assessment_id: selectedQuestion.assessment_id,
          answer: completedSentence.replaceAll('_', ''),
        }],
      }

      const result = await aiAssessmentRepository.testPrompt(request)
      onSuccess(result)
    }
    catch (err: any) {
      onError(err.message || 'Failed to submit test')
    }
    finally {
      loadingTest.value = false
    }
  }

  return {
    // State
    loadingTest,

    // Actions
    submitTest,
  }
}
