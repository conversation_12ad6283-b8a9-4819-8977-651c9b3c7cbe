import type { Staff, StaffAuthData, StaffLoginCredentials } from '@/types/staff'
import { TOKEN_AUTH_KEY } from '#shared/constants'
import { staffAuthRepository } from '@/repositories/auth'

/**
 * Staff Authentication Composable
 * Handles authentication logic, state management, and token storage
 */
export function useStaffAuth() {
  const token = useCookie<string | null>(TOKEN_AUTH_KEY, {
    default: () => null,
    maxAge: 60 * 60 * 24 * 7, // 7 days
    secure: true,
    sameSite: 'strict',
  })

  const staff = useState<Staff | null>('staff', () => null)
  const isAuthenticated = computed(() => !!token.value && !!staff.value)

  /**
   * Fetch the currently authenticated staff member
   * This should be called on app startup to restore session
   */
  async function fetchUser() {
    if (token.value && !staff.value) {
      try {
        const response = await staffAuthRepository.me()
        staff.value = response
      }
      catch (error) {
        console.error('Failed to fetch user, clearing auth state.', error)
        // clearAuth()
      }
    }
  }

  /**
   * Login staff member with username and password
   * @param credentials - Staff login credentials (username, password)
   * @returns Promise resolving to staff auth data
   */
  async function login(credentials: StaffLoginCredentials): Promise<StaffAuthData> {
    try {
      const response = await staffAuthRepository.login(credentials)

      token.value = response.token
      staff.value = response.staff

      return response
    }
    catch (error: any) {
      // Clear any existing auth state on login failure
      token.value = null
      staff.value = null
      throw error
    }
  }

  /**
   * Logout the currently authenticated staff member
   * @returns Promise resolving to logout response
   */
  async function logout(): Promise<void> {
    try {
      if (token.value) {
        // Call the logout endpoint to invalidate the token on the server
        await staffAuthRepository.logout()
      }
    }
    catch (error) {
      // Continue with local logout even if server request fails
      console.warn('Server logout failed, proceeding with local logout:', error)
    }
    finally {
      // Always clear local auth state
      token.value = null
      staff.value = null
    }
  }

  /**
   * Check if staff member is currently authenticated
   * @returns boolean indicating authentication status
   */
  function checkAuth(): boolean {
    return isAuthenticated.value
  }

  /**
   * Get current staff member data
   * @returns Current staff object or null
   */
  function getCurrentStaff(): Staff | null {
    return staff.value
  }

  /**
   * Get current authentication token
   * @returns Current token string or null
   */
  function getToken(): string | null {
    return token.value
  }

  /**
   * Clear authentication state (local only)
   * Useful for handling token expiry or forced logout
   */
  function clearAuth(): void {
    token.value = null
    staff.value = null
  }

  return {
    // State
    staff: readonly(staff),
    isAuthenticated,

    // Actions
    login,
    logout,
    fetchUser,
    checkAuth,
    getCurrentStaff,
    getToken,
    clearAuth,
  }
}

/**
 * Composable for staff authentication with loading states
 * Provides additional loading state management for auth operations
 */
export function useStaffAuthState() {
  const auth = useStaffAuth()
  const { loading, error, execute, reset } = useAPIState()

  /**
   * Login with loading state management
   */
  async function loginWithState(credentials: StaffLoginCredentials) {
    return execute(() => auth.login(credentials))
  }

  /**
   * Logout with loading state management
   */
  async function logoutWithState() {
    return execute(() => auth.logout())
  }

  return {
    // Auth functions
    ...auth,

    // Loading states
    loading,
    error,
    reset,

    // State-managed actions
    loginWithState,
    logoutWithState,
  }
}
