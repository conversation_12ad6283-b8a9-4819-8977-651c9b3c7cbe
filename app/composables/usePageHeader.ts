export interface BreadcrumbItem {
  label: string
  href?: string
  active?: boolean
}

export interface PageHeaderConfig {
  title?: string
  breadcrumbs?: BreadcrumbItem[]
  showAuthStatus?: boolean
}

export function usePageHeader() {
  const headerConfig = useState<PageHeaderConfig>('page-header', () => ({
    title: '',
    breadcrumbs: [],
    showAuthStatus: false,
  }))

  const setHeader = (config: PageHeaderConfig) => {
    headerConfig.value = { ...headerConfig.value, ...config }
  }

  const setBreadcrumbs = (breadcrumbs: BreadcrumbItem[]) => {
    headerConfig.value.breadcrumbs = breadcrumbs
  }

  const setTitle = (title: string) => {
    headerConfig.value.title = title
  }

  function setHeaderConfig(config: PageHeaderConfig) {
    headerConfig.value = config
  }

  return {
    headerConfig: readonly(headerConfig),
    setHeader,
    setBreadcrumbs,
    setTitle,

    setHeaderConfig,
  }
}
