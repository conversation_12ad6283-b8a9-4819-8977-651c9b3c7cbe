import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'

type QueryParams = Record<string, string | string[] | undefined>

/**
 * A composable for reactively managing URL query parameters.
 */
export function useQueryParams() {
  const route = useRoute()
  const router = useRouter()

  /**
   * A computed property that reflects the current URL query parameters.
   */
  const queryParams = computed(() => route.query)

  /**
   * Updates the URL query parameters by merging the provided query
   * with the existing ones.
   *
   * @param query - An object representing the query parameters to set.
   */
  function setQueryParam(query: QueryParams) {
    const newQuery = { ...route.query, ...query }

    // Clean up undefined properties so they are removed from the URL
    const finalQuery: Record<string, any> = {}
    for (const key in newQuery) {
      if (newQuery[key] !== undefined) {
        finalQuery[key] = newQuery[key]
      }
    }

    router.push({ query: finalQuery })
  }

  return {
    queryParams,
    setQueryParam,
  }
}
