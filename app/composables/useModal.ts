import type { ModalButton, ModalConfig, ModalState, UseModalReturn } from '~/types/modal'

// Global state for modal system
const modalState = reactive<ModalState>({
  isOpen: false,
  config: null,
  loading: false,
})

// Promise resolvers for different modal types
let currentResolver: ((value: any) => void) | null = null

export function useModal(): UseModalReturn {
  /**
   * Open a modal with the provided configuration
   */
  const open = (config: ModalConfig) => {
    modalState.config = {
      size: 'md',
      variant: 'default',
      showCloseButton: true,
      persistent: false,
      ...config,
      id: config.id || `modal-${Date.now()}`,
      // Use markRaw for component to prevent reactivity
      customComponent: config.customComponent ? markRaw(config.customComponent) : config.customComponent,
    }
    modalState.isOpen = true
    modalState.loading = false
  }

  /**
   * Close the currently open modal
   */
  const close = async (): Promise<void> => {
    if (modalState.config?.onClose) {
      try {
        await modalState.config.onClose()
      }
      catch (error) {
        console.error('Error in modal onClose:', error)
      }
    }

    modalState.isOpen = false
    modalState.config = null
    modalState.loading = false

    // Resolve any pending promises with null/false
    if (currentResolver) {
      currentResolver(false)
      currentResolver = null
    }

    // Clear global resolver
    if (process.client) {
      (window as any).__modalResolver = null
    }
  }

  /**
   * Show a confirmation dialog
   * Returns a promise that resolves to true if confirmed, false if cancelled
   */
  const confirm = (config: Omit<ModalConfig, 'buttons'>): Promise<boolean> => {
    return new Promise((resolve) => {
      currentResolver = resolve

      const confirmButtons: ModalButton[] = [
        {
          text: 'Cancel',
          variant: 'outline',
          action: () => {
            resolve(false)
            currentResolver = null
            close()
          },
        },
        {
          text: 'Confirm',
          variant: config.variant === 'destructive' ? 'destructive' : 'default',
          action: () => {
            resolve(true)
            currentResolver = null
            close()
          },
        },
      ]

      open({
        ...config,
        variant: config.variant || 'default',
        buttons: confirmButtons,
        onClose: () => {
          resolve(false)
          currentResolver = null
        },
      })
    })
  }

  /**
   * Show an alert dialog
   * Returns a promise that resolves when the alert is dismissed
   */
  const alert = (config: Omit<ModalConfig, 'buttons'>): Promise<void> => {
    return new Promise((resolve) => {
      currentResolver = resolve

      const alertButtons: ModalButton[] = [
        {
          text: 'OK',
          variant: 'default',
          action: () => {
            resolve()
            currentResolver = null
            close()
          },
        },
      ]

      open({
        ...config,
        buttons: alertButtons,
        onClose: () => {
          resolve()
          currentResolver = null
        },
      })
    })
  }

  /**
   * Show a custom component in a modal
   * Returns a promise that resolves with the component's emitted data
   */
  const custom = (
    component: any,
    props: Record<string, any> = {},
    config: Partial<ModalConfig> = {},
  ): Promise<any> => {
    return new Promise((resolve) => {
      currentResolver = resolve

      // Set the global resolver for custom components
      if (process.client) {
        (window as any).__modalResolver = resolve
      }

      open({
        size: 'md',
        variant: 'default',
        showCloseButton: true,
        ...config,
        customComponent: markRaw(component),
        customProps: props,
        onClose: () => {
          resolve(null)
          currentResolver = null
          if (process.client) {
            (window as any).__modalResolver = null
          }
        },
      })
    })
  }

  /**
   * Check if a modal is currently open
   */
  const isModalOpen = (id?: string): boolean => {
    if (!modalState.isOpen)
      return false
    if (!id)
      return true
    return modalState.config?.id === id
  }

  /**
   * Set loading state for the modal
   */
  const setLoading = (loading: boolean) => {
    modalState.loading = loading
  }

  return {
    // Readonly state
    isOpen: readonly(toRef(modalState, 'isOpen')),
    config: toRef(modalState, 'config'),
    loading: readonly(toRef(modalState, 'loading')),

    // Methods
    open,
    close,
    confirm,
    alert,
    custom,

    // Utilities
    isModalOpen,
    setLoading,
  }
}

// Helper function for custom components to resolve the modal
export function resolveModal(value: any) {
  if (process.client && (window as any).__modalResolver) {
    const resolver = (window as any).__modalResolver

    // Clear the resolvers
    ;(window as any).__modalResolver = null
    currentResolver = null

    // Close the modal
    modalState.isOpen = false
    modalState.config = null
    modalState.loading = false

    // Resolve the promise
    if (resolver) {
      resolver(value)
    }
  }
}
