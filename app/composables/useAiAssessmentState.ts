import type { AiAssessmentItem, TestPromptResponse } from '@/repositories/aiAssessment'
import { aiAssessmentRepository } from '@/repositories/aiAssessment'

/**
 * Composable for managing AI assessment state and data fetching
 */
export function useAiAssessmentState() {
  // State management
  const selectedType = ref<string | ''>('')
  const selectedQuestion = ref<AiAssessmentItem | null>(null)
  const loadingQuestions = ref(false)
  const error = ref<string | null>(null)
  const questions = ref<AiAssessmentItem[]>([])
  const testResult = ref<TestPromptResponse | null>(null)

  // Fetch questions by type
  async function fetchQuestions(type: string) {
    try {
      loadingQuestions.value = true
      error.value = null
      questions.value = await aiAssessmentRepository.fetchAssessmentsByType(type)
      selectedQuestion.value = null
    }
    catch (err: any) {
      error.value = err.message || 'Failed to fetch questions'
      questions.value = []
    }
    finally {
      loadingQuestions.value = false
    }
  }

  // Clear all selections and start over
  function clearAll() {
    selectedType.value = ''
    selectedQuestion.value = null
    questions.value = []
    error.value = null
    testResult.value = null
  }

  // Set test result
  function setTestResult(result: TestPromptResponse) {
    testResult.value = result
  }

  // Set error
  function setError(errorMessage: string) {
    error.value = errorMessage
  }

  return {
    // State
    selectedType,
    selectedQuestion,
    loadingQuestions,
    error,
    questions,
    testResult,

    // Actions
    fetchQuestions,
    clearAll,
    setTestResult,
    setError,
  }
}
