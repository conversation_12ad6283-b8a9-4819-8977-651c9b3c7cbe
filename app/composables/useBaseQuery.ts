import { useQueryParams } from '~/composables/useQueryParams'

/**
 * Provides common actions for lists that require searching and pagination.
 */
export function useBaseQuery() {
  const { queryParams, setQueryParam } = useQueryParams()

  /**
   * Sets the search query parameter and resets to the first page.
   * @param query - The search string.
   */
  function search(query: string) {
    setQueryParam({ q: query || undefined, page: undefined })
  }

  /**
   * Navigates to the next page.
   */
  function nextPage() {
    const currentPage = Number.parseInt(queryParams.value.page as string || '1')
    setQueryParam({ page: (currentPage + 1).toString() })
  }

  /**
   * Navigates to the previous page.
   */
  function prevPage() {
    const currentPage = Number.parseInt(queryParams.value.page as string || '1')
    if (currentPage <= 1)
      return
    const newPage = currentPage - 1
    setQueryParam({ page: newPage > 1 ? newPage.toString() : undefined })
  }

  /**
   * Navigates to a specific page.
   * @param page - The page number to navigate to.
   */
  function goToPage(page: number) {
    if (page < 1)
      return
    setQueryParam({ page: page > 1 ? page.toString() : undefined })
  }

  return {
    search,
    nextPage,
    prevPage,
    goToPage,
  }
}
