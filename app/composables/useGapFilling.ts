import type { AiAssessmentItem } from '@/repositories/aiAssessment'

/**
 * Composable for managing gap filling functionality in AI assessments
 */
export function useGapFilling() {
  // Form state for gap filling
  const gapAnswers = ref<string[]>([])
  const questionParts = ref<{ text: string, isGap: boolean }[]>([])
  const gapIndices = ref<number[]>([])

  // Parse question text based on fill_position array
  function parseQuestionForGaps(questionText: string, fillPosition?: number[]) {
    if (!fillPosition) {
      questionParts.value = []
      gapAnswers.value = []
      gapIndices.value = []
      return
    }

    const parts: { text: string, isGap: boolean }[] = []
    const indices: number[] = []
    const fillPositions = [...fillPosition].sort((a, b) => a - b)
    let lastPosition = 0
    let gapIndex = 0

    // Split text based on fill_position array
    fillPositions.forEach((position) => {
      // Add text before the gap
      if (position > lastPosition) {
        const textBefore = questionText.slice(lastPosition, position)
        if (textBefore.length > 0) {
          parts.push({ text: textBefore, isGap: false })
          indices.push(-1) // -1 for non-gap parts
        }
      }

      // Add the gap
      parts.push({ text: '', isGap: true })
      indices.push(gapIndex++) // Store the gap index
      lastPosition = position
    })

    // Add remaining text after the last gap
    if (lastPosition < questionText.length) {
      const remainingText = questionText.slice(lastPosition)
      if (remainingText.length > 0) {
        parts.push({ text: remainingText, isGap: false })
        indices.push(-1) // -1 for non-gap parts
      }
    }

    questionParts.value = parts
    gapIndices.value = indices

    // Initialize gap answers array based on number of gaps found
    const gapCount = fillPositions.length
    gapAnswers.value = Array.from({ length: gapCount }, () => '')
  }

  // Reset answer form
  function resetAnswerForm() {
    gapAnswers.value = []
    questionParts.value = []
    gapIndices.value = []
  }

  // Get the gap index for a given part index using simple array lookup
  function getGapIndex(partIndex: number): number {
    return gapIndices.value[partIndex] ?? 0
  }

  // Reconstruct completed sentence using fill_position array
  function reconstructCompletedSentence(question: AiAssessmentItem): string {
    if (!question.fill_position) {
      return question.question || ''
    }

    const fillPositions = [...question.fill_position].sort((a, b) => a - b)
    let result = question.question
    let offset = 0

    // Insert answers at their corresponding positions
    fillPositions.forEach((position, index) => {
      const answer = gapAnswers.value[index] || ''
      const insertPosition = position + offset
      result = result.slice(0, insertPosition) + answer + result.slice(insertPosition)
      offset += answer.length
    })

    return result
  }

  // Update gap answer
  function updateGapAnswer(index: number, value: string) {
    gapAnswers.value[index] = value
  }

  // Check if all gaps are filled
  const allGapsFilled = computed(() => {
    return gapAnswers.value.length > 0 && gapAnswers.value.every(answer => answer.trim().length > 0)
  })

  return {
    // State
    gapAnswers,
    questionParts,
    gapIndices,
    allGapsFilled,

    // Actions
    parseQuestionForGaps,
    resetAnswerForm,
    getGapIndex,
    reconstructCompletedSentence,
    updateGapAnswer,
  }
}
