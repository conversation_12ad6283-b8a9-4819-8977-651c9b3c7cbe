/**
 * Represents a staff member entity as returned from the API.
 */
export interface Staff {
  id: number
  username: string
  created_at: string
  updated_at: string
}

/**
 * Defines the credentials required for a staff member to log in.
 */
export interface StaffLoginCredentials {
  username: string
  password: string
}

/**
 * Defines the structure of the authentication data within the API response,
 * containing the staff member's details and a JWT.
 */
export interface StaffAuthData {
  staff: Staff
  token: string
}
