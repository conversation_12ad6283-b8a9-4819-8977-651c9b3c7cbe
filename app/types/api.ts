// Base API Response Types
export interface ApiResponse<T = any> {
  data: T
  message?: string
  status: number
  success: boolean
}

export interface ApiError {
  message: string
  statusCode: number
  data?: any
  errors?: Record<string, string[]>
}

export interface PaginatedResponse<T> {
  data: T[]
  meta: {
    total: number
    page: number
    limit: number
    totalPages: number
  }
  links: {
    first?: string
    last?: string
    prev?: string
    next?: string
  }
}

// Laravel-style pagination response
export interface LaravelPaginatedResponse<T> {
  current_page: number
  data: T[]
  first_page_url: string
  from: number
  last_page: number
  last_page_url: string
  links: Array<{
    url: string | null
    label: string
    active: boolean
  }>
  next_page_url: string | null
  path: string
  per_page: number
  prev_page_url: string | null
  to: number
  total: number
}

export interface ApiRequestOptions {
  headers?: Record<string, string>
  query?: Record<string, any>
  body?: any
  method?: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'
  transform?: (data: any) => any
  lazy?: boolean
  server?: boolean
  key?: string
}

// Utility types for the API client
export type ApiEndpoint = string | (() => string)
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE'
