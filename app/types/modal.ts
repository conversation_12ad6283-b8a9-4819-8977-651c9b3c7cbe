export interface ModalButton {
  text: string
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'default' | 'sm' | 'lg' | 'icon'
  action?: () => void | Promise<void>
  loading?: boolean
  disabled?: boolean
  icon?: string
}

export interface ModalConfig {
  id?: string
  title?: string
  description?: string
  content?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  variant?: 'default' | 'destructive' | 'success' | 'warning' | 'info'
  showCloseButton?: boolean
  persistent?: boolean // Cannot be closed by clicking outside or pressing ESC
  buttons?: ModalButton[]
  onClose?: () => void | Promise<void>
  customComponent?: any // Vue component
  customProps?: Record<string, any>
}

export interface ModalState {
  isOpen: boolean
  config: ModalConfig | null
  loading: boolean
}

export interface UseModalReturn {
  // State
  isOpen: Readonly<Ref<boolean>>
  config: Readonly<Ref<ModalConfig | null>>
  loading: Readonly<Ref<boolean>>

  // Methods
  open: (config: ModalConfig) => void
  close: () => Promise<void>
  confirm: (config: Omit<ModalConfig, 'buttons'>) => Promise<boolean>
  alert: (config: Omit<ModalConfig, 'buttons'>) => Promise<void>
  custom: (component: any, props?: Record<string, any>, config?: Partial<ModalConfig>) => Promise<any>

  // Utilities
  isModalOpen: (id?: string) => boolean
  setLoading: (loading: boolean) => void
}
