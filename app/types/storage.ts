// Storage API Types

/**
 * Request body for deleting a file
 */
export interface DeleteFileRequest {
  path: string
}

/**
 * Response from file deletion
 */
export interface DeleteFileResponse {
  message: string
}

/**
 * Request body for generating presigned upload URL
 */
export interface PresignedUploadUrlRequest {
  extension: string
}

/**
 * Response containing presigned upload URL and metadata
 */
export interface PresignedUploadUrlResponse {
  presigned_url: string
  public_url: string
  file_path: string
  filename: string
  expires_in: number
}

/**
 * Request body for generating presigned download URL
 */
export interface PresignedDownloadUrlRequest {
  path: string
}

/**
 * Response containing presigned download URL and metadata
 */
export interface PresignedDownloadUrlResponse {
  download_url: string
  expires_in: number
  path: string
}

export type FileType = 'audio' | 'image' | 'video' | 'document' | 'other'

export interface FileRecord {
  path: string
  id: number
  type: FileType
}
