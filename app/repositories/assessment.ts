import type { Assessmentable, AssessmentAnswerTheQuestion, AssessmentGapFill, AssessmentWordOrder, CreateAssessmentData, UpdateAssessmentData } from '@/types/course'
import type { FileRecord, FileType } from '@/types/storage'
import { isAnswerTheQuestionAssessment, isGapFillAssessment, isWordOrderAssessment } from '@/utils/assessment'

/**
 * Assessment Management Repository
 * Pure API calls for assessment management endpoints
 * Handles both admin and public assessment operations
 */
export const assessmentRepository = {
  /**
   * Create a new assessment (Admin)
   * @param assessmentData - Assessment creation data with type
   * @returns Promise resolving to created assessment
   */
  async createAssessment(assessmentData: CreateAssessmentData & { type: string }): Promise<Assessmentable> {
    const { call } = useAPICall()
    return await call<Assessmentable>('/api/admin/assessments', {
      method: 'POST',
      body: assessmentData,
    })
  },

  /**
   * Update assessment (Admin)
   * @param type - Assessment type
   * @param id - Assessment ID
   * @param assessmentData - Assessment update data
   * @returns Promise resolving to updated assessment
   */
  async updateAssessment(type: string, id: number, assessmentData: UpdateAssessmentData): Promise<Assessmentable> {
    const { call } = useAPICall()
    return await call<Assessmentable>(`/api/admin/assessments/${type}/${id}`, {
      method: 'PUT',
      body: assessmentData,
    })
  },

  /**
   * Delete assessment (Admin)
   * @param type - Assessment type
   * @param id - Assessment ID
   * @returns Promise resolving to deletion response
   */
  async deleteAssessment(type: string, id: number): Promise<void> {
    const { call } = useAPICall()
    return await call<void>(`/api/admin/assessments/${type}/${id}`, {
      method: 'DELETE',
    })
  },

  /**
   * Clear all assessments from a unit (Admin)
   * @param unitId - Unit ID
   * @returns Promise resolving to deletion response with count
   */
  async clearUnitAssessments(unitId: number): Promise<{ deleted_count: number }> {
    const { call } = useAPICall()
    return await call<{ deleted_count: number }>(`/api/admin/units/${unitId}/assessments`, {
      method: 'DELETE',
    })
  },

  /**
   * Score an assessment (Public)
   * @param assessmentId - Assessment ID
   * @param answer - User's answers (array of indexes or values)
   * @returns Promise resolving to scoring results
   */
  async score(
    assessmentOrId: number | AssessmentGapFill | AssessmentAnswerTheQuestion | AssessmentWordOrder,
    answer: (number | string)[],
    type?: string,
  ): Promise<{
    pass: boolean
    explain: string
    meta?: any
  }> {
    // Helper to normalize free-text answers (trim, collapse spaces, case-insensitive)
    const normalize = (val: string) => val
      .trim()
      .toLowerCase()
      .replace(/\s+/g, ' ')

    // Local scoring for GapFill and AnswerTheQuestion when full assessment is provided
    // Word Order assessments always use API scoring for detailed feedback
    if (typeof assessmentOrId !== 'number') {
      const assessment = assessmentOrId as AssessmentGapFill | AssessmentAnswerTheQuestion | AssessmentWordOrder
      // Word Order: always use API for detailed word analysis
      if (isWordOrderAssessment(assessment as unknown as Assessmentable)) {
        if (!type) {
          type = 'word-order'
        }
        const { call } = useAPICall()
        return await call<{
          pass: boolean
          explain: string
          meta?: any
        }>(`/api/public/assessments/${type}/score`, {
          method: 'POST',
          body: {
            assessment_id: assessment.id,
            answer: [Array.isArray(answer) ? answer.join(' ') : String(answer)],
          },
        })
      }

      // Gap Fill: all gaps must match (case-insensitive, trimmed). Partial credit impacts UI score elsewhere.
      if (isGapFillAssessment(assessment as unknown as Assessmentable)) {
        const gap = assessment as AssessmentGapFill
        const userAnswers = (answer as string[]).map((a: string | number) => normalize(String(a)))
        const correctAnswers = gap.correct_answers.map((a: string) => normalize(a))

        // Determine pass as all gaps correct
        const allCorrect = correctAnswers.every((corr: string, idx: number) => userAnswers[idx] === corr)

        // Explanation preference: use assessment.explanation when provided; otherwise show correct answers on fail
        let explain = gap.explanation || ''
        if (!explain && !allCorrect) {
          const correctAnswersText = gap.correct_answers.map((ans: string, i: number) => `${i + 1}. ${ans}`).join(', ')
          explain = `Correct answers: ${correctAnswersText}`
        }

        return { pass: allCorrect, explain }
      }

      // Answer The Question: pass if any acceptable answer matches after normalization
      if (isAnswerTheQuestionAssessment(assessment as unknown as Assessmentable)) {
        const atq = assessment as AssessmentAnswerTheQuestion
        const userAnswer = normalize(String((answer[0] ?? '')))
        const accepted = atq.correct_answers.map((a: string) => normalize(a))
        const pass = accepted.includes(userAnswer)
        const explain = atq.explanation || ''
        return { pass, explain }
      }

      // If we received an assessment object of a different type, fall back to API if type string is provided
      if (!type) {
        throw new Error('Unsupported assessment type for local scoring and missing API type parameter')
      }

      const { call } = useAPICall()
      return await call<{
        pass: boolean
        explain: string
        meta?: any
      }>(`/api/public/assessments/${type}/score`, {
        method: 'POST',
        body: {
          assessment_id: (assessment as any).id,
          answer,
        },
      })
    }

    // Default: maintain existing behavior (API-based scoring)
    const assessmentId = assessmentOrId
    if (!type) {
      throw new Error('Assessment type is required when scoring by assessment ID')
    }

    const { call } = useAPICall()
    return await call<{
      pass: boolean
      explain: string
      meta?: any
    }>(`/api/public/assessments/${type}/score`, {
      method: 'POST',
      body: {
        assessment_id: assessmentId,
        answer: type === 'word-order' && Array.isArray(answer) ? answer.join(' ') : answer,
      },
    })
  },

  async attachFileToAssessment(url: string, assessmentId: number, type: FileType): Promise<FileRecord> {
    const { call } = useAPICall()

    const fileRecord = await call<FileRecord>(`/api/admin/assessments/attach-file/${assessmentId}`, {
      method: 'POST',
      body: {
        url,
        type,
      },
    })

    return fileRecord
  },

  async detachFileFromAssessment(fileId: number, assessmentId: number): Promise<void> {
    const { call } = useAPICall()
    return await call<void>(`/api/admin/assessments/detach-file/${assessmentId}`, {
      method: 'DELETE',
      body: { file_id: fileId },
    })
  },
}
