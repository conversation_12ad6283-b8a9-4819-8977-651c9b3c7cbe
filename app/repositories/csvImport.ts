import type { ImportResponse, Unit } from '@/types/course'
import { TOKEN_AUTH_KEY } from '#shared/constants'

/**
 * File Import Repository
 * Pure API calls for file import functionality
 * Handles file uploads and validation for course content import
 * Supports both CSV and XLSX formats
 */
export const csvImportRepository = {
  /**
   * Import CSV file (Admin)
   * @param csvFile - CSV file to import
   * @param unitId - Target unit ID
   * @returns Promise resolving to import results
   */
  async importCSV(csvFile: File, unitId: number): Promise<ImportResponse> {
    const config = useRuntimeConfig()
    const tokenCookie = useCookie(TOKEN_AUTH_KEY, { default: () => '' })

    const formData = new FormData()
    formData.append('file', csvFile)

    // Ensure we have a valid token
    if (!tokenCookie.value) {
      throw new Error('Authentication token is required')
    }

    return await $fetch<ImportResponse>(`/api/admin/units/${unitId}/import/xlsx`, {
      baseURL: config.public.apiBaseUrl as string,
      method: 'POST',
      headers: {
        Authorization: `Bearer ${tokenCookie.value}`,
        Accept: 'application/json',
      },
      body: formData,
    })
  },

  /**
   * Download XLSX template (Admin)
   * @param assessment_type - Assessment type for template
   * @param unitId - Unit ID to get unit name for filename
   * @returns Promise resolving to XLSX template file
   */
  async downloadTemplate(assessment_type: string, unitId?: number): Promise<{ blob: Blob, unitName?: string }> {
    const config = useRuntimeConfig()
    const tokenCookie = useCookie(TOKEN_AUTH_KEY, { default: () => '' })

    // Get unit name if unitId is provided
    let unitName: string | undefined
    if (unitId) {
      try {
        const unitResponse = await $fetch<Unit>(`/api/admin/units/${unitId}`, {
          baseURL: config.public.apiBaseUrl as string,
          method: 'GET',
          headers: {
            Authorization: tokenCookie.value ? `Bearer ${tokenCookie.value}` : '',
            Accept: 'application/json',
          },
        })
        unitName = unitResponse.title
      }
      catch (error) {
        console.warn('Failed to fetch unit name:', error)
      }
    }

    // Use $fetch directly to avoid automatic JSON headers
    const response = await $fetch(`/api/admin/import/template/${assessment_type}`, {
      baseURL: config.public.apiBaseUrl as string,
      method: 'GET',
      headers: {
        Authorization: tokenCookie.value ? `Bearer ${tokenCookie.value}` : '',
        Accept: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/octet-stream,*/*',
      },
      responseType: 'blob',
    })

    return { blob: response as Blob, unitName }
  },

  /**
   * Helper method to create a download link for the XLSX template
   * @param assessment_type - Assessment type for template
   * @param unitId - Optional unit ID to get unit name for filename
   * @returns Promise that triggers the template download
   */
  async downloadTemplateFile(assessment_type: string, unitId?: number): Promise<void> {
    try {
      const { blob, unitName } = await this.downloadTemplate(assessment_type, unitId)

      // Create a temporary URL for the blob
      const url = window.URL.createObjectURL(blob)

      // Generate filename based on unit name or default
      const filename = unitName
        ? `${unitName.replace(/[^a-z0-9\s-]/gi, '').replace(/\s+/g, '_')}_template.xlsx`
        : 'import_template.xlsx'

      // Create a temporary anchor element and trigger download
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()

      // Clean up
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }
    catch (error) {
      console.error('Failed to download template:', error)
      throw error
    }
  },

  /**
   * Helper method to validate file before upload
   * @param file - File to validate
   * @returns Boolean indicating if file is valid
   */
  validateFile(file: File): { isValid: boolean, error?: string } {
    // Check file type - now accepting both CSV and XLSX
    const allowedTypes = [
      'text/csv',
      'text/plain',
      'application/csv',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
    ]
    const allowedExtensions = ['.csv', '.txt', '.xlsx', '.xls']

    const hasValidType = allowedTypes.includes(file.type)
    const hasValidExtension = allowedExtensions.some(ext =>
      file.name.toLowerCase().endsWith(ext),
    )

    if (!hasValidType && !hasValidExtension) {
      return {
        isValid: false,
        error: 'Invalid file type. Please upload a CSV, TXT, or XLSX file.',
      }
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB in bytes
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size exceeds 10MB limit.',
      }
    }

    return { isValid: true }
  },
}
