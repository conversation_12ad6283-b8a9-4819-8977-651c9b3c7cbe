import type {
  DeleteFileRequest,
  DeleteFileResponse,
  PresignedDownloadUrlRequest,
  PresignedDownloadUrlResponse,
  PresignedUploadUrlRequest,
  PresignedUploadUrlResponse,
} from '@/types/storage'

/**
 * Storage Management Repository
 * Pure API calls for storage management endpoints
 * Handles file operations including deletion and presigned URL generation
 */
export const storageRepository = {
  /**
   * Delete a file at the specified path
   * @param path - File path to delete
   * @returns Promise resolving to deletion response
   */
  async deleteFile(path: string): Promise<DeleteFileResponse> {
    const { call } = useAPICall()
    return await call<DeleteFileResponse>('/api/v1/storage/delete', {
      method: 'DELETE',
      body: { path } as DeleteFileRequest,
    })
  },

  async generatePresignedTempUploadUrl(
    extension: string,
  ): Promise<PresignedUploadUrlResponse> {
    const { call } = useAPICall()
    const presignedUrl = await call<PresignedUploadUrlResponse>('/api/v1/storage/presigned-temp-upload-url', {
      method: 'POST',
      body: {
        extension,
      } as PresignedUploadUrlRequest,
    })
    return presignedUrl
  },

  /**
   * Generate a presigned download URL for file access
   * @param path - File path for download
   * @param expiresIn - URL expiration time in seconds (default: 900)
   * @returns Promise resolving to presigned download URL response
   */
  async generatePresignedDownloadUrl(
    path: string,
  ): Promise<PresignedDownloadUrlResponse> {
    const { call } = useAPICall()
    return await call<PresignedDownloadUrlResponse>('/api/v1/storage/presigned-download-url', {
      method: 'POST',
      body: {
        path,
      } as PresignedDownloadUrlRequest,
    })
  },

  async uploadByPresignedUrl(
    presignedUrl: string,
    file: File,
  ): Promise<boolean> {
    try {
      const response = await fetch(presignedUrl, {
        method: 'PUT',
        body: file,
        headers: {
          'Content-Type': file.type,
        },
      })

      return Boolean(response.ok)
    }
    catch (error) {
      console.error('Upload error:', error)
      return false
    }
  },
}
