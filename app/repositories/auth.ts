import type { Staff, StaffAuthData, StaffLoginCredentials } from '@/types/staff'

/**
 * Staff Authentication Repository
 * Pure API calls for staff authentication endpoints
 * Updated to match the new admin API documentation
 */
export const staffAuthRepository = {
  /**
   * Staff login API call
   * @param credentials - Staff login credentials (username, password)
   * @returns Promise resolving to login response data
   */
  async login(credentials: StaffLoginCredentials): Promise<StaffAuthData> {
    const { call } = useAPICall()
    const response = await call<StaffAuthData>('/api/admin/login', {
      method: 'POST',
      body: credentials,
    })
    return response
  },

  /**
   * Staff logout API call
   * @returns Promise resolving to logout response
   */
  async logout(): Promise<Staff> {
    const { call } = useAPICall()
    return await call<Staff>('/api/admin/logout', {
      method: 'POST',
    })
  },

  /**
   * Get the currently authenticated staff member
   * @returns Promise resolving to the staff member
   */
  async me(): Promise<Staff> {
    const { call } = useAPICall()
    const response = await call<Staff>('/api/admin/me', {
      method: 'GET',
    })
    return response
  },
}
