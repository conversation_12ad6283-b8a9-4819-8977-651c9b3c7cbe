import type {
  Course,
  CourseListQuery,
  CourseListResponse,
  CourseStatsResponse,
  CreateCourseData,
  UpdateCourseData,
} from '@/types/course'

/**
 * Course Management Repository
 * Pure API calls for course management endpoints
 * Handles both admin and public course operations
 */
export const courseRepository = {
  /**
   * List all courses (Admin)
   * @param query - Query parameters for filtering and pagination
   * @returns Promise resolving to paginated course list
   */
  async listCourses(query?: CourseListQuery): Promise<CourseListResponse> {
    const { call } = useAPICall()
    return await call<CourseListResponse>('/api/admin/courses', {
      method: 'GET',
      query,
    })
  },

  /**
   * Browse courses (Public)
   * @returns Promise resolving to course list
   */
  async browseCourses(): Promise<CourseListResponse> {
    const { call } = useAPICall()
    return await call<CourseListResponse>('/api/public/courses', {
      method: 'GET',
    })
  },

  /**
   * Create a new course (Admin)
   * @param courseData - Course creation data
   * @returns Promise resolving to created course
   */
  async createCourse(courseData: CreateCourseData): Promise<Course> {
    const { call } = useAPICall()
    return await call<Course>('/api/admin/courses', {
      method: 'POST',
      body: courseData,
    })
  },

  /**
   * Get course details (Admin)
   * @param id - Course ID
   * @returns Promise resolving to course details
   */
  async getCourse(id: number): Promise<Course> {
    const { call } = useAPICall()
    return await call<Course>(`/api/admin/courses/${id}`, {
      method: 'GET',
    })
  },

  /**
   * Get course details (Public)
   * @param id - Course ID
   * @returns Promise resolving to course details
   */
  async getPublicCourse(id: number): Promise<Course> {
    const { call } = useAPICall()
    return await call<Course>(`/api/public/courses/${id}`, {
      method: 'GET',
    })
  },

  /**
   * Update course (Admin)
   * @param id - Course ID
   * @param courseData - Course update data
   * @returns Promise resolving to updated course
   */
  async updateCourse(id: number, courseData: UpdateCourseData): Promise<Course> {
    const { call } = useAPICall()
    return await call<Course>(`/api/admin/courses/${id}`, {
      method: 'PUT',
      body: courseData,
    })
  },

  /**
   * Partially update course (Admin)
   * @param id - Course ID
   * @param courseData - Course update data
   * @returns Promise resolving to updated course
   */
  async patchCourse(id: number, courseData: UpdateCourseData): Promise<Course> {
    const { call } = useAPICall()
    return await call<Course>(`/api/admin/courses/${id}`, {
      method: 'PATCH',
      body: courseData,
    })
  },

  /**
   * Delete course (Admin)
   * @param id - Course ID
   * @returns Promise resolving to deletion response
   */
  async deleteCourse(id: number): Promise<void> {
    const { call } = useAPICall()
    return await call<void>(`/api/admin/courses/${id}`, {
      method: 'DELETE',
    })
  },

  /**
   * Duplicate course (Admin)
   * @param id - Course ID to duplicate
   * @returns Promise resolving to duplicated course
   */
  async duplicateCourse(id: number): Promise<Course> {
    const { call } = useAPICall()
    return await call<Course>(`/api/admin/courses/${id}/duplicate`, {
      method: 'POST',
    })
  },

  /**
   * Get courses with statistics (Admin)
   * @returns Promise resolving to courses with stats
   */
  async getCoursesWithStats(): Promise<CourseStatsResponse> {
    const { call } = useAPICall()
    return await call<CourseStatsResponse>('/api/admin/courses-with-stats', {
      method: 'GET',
    })
  },

  async getPublicCoursesWithStats(): Promise<Course[]> {
    const { call } = useAPICall()
    return await call<Course[]>('/api/public/courses', {
      method: 'GET',
    })
  },
}
