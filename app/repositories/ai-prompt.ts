import type {
  Ai<PERSON>rom<PERSON>,
  AiPromptListQuery,
  AiPromptListResponse,
  UpdateAiPromptData,
} from '@/types/ai-prompt'

/**
 * AI Prompt Management Repository
 * Pure API calls for AI prompt management endpoints
 * Handles CRUD operations for AI prompt templates
 */
export const aiPromptRepository = {
  /**
   * Get all AI prompts (Admin)
   * @param query - Query parameters for filtering and pagination
   * @returns Promise resolving to paginated AI prompt list
   */
  async getAllAiPrompts(query?: AiPromptListQuery): Promise<AiPromptListResponse> {
    const { call } = useAPICall()
    const response = await call<AiPromptListResponse>('/api/admin/ai-prompts', {
      method: 'GET',
      query,
    })

    return response
  },

  /**
   * Update AI prompt (Admin)
   * @param promptType - The prompt type identifier
   * @param data - AI prompt update data
   * @returns Promise resolving to updated AI prompt
   */
  async updateAiPrompt(promptType: string, data: UpdateAiPromptData): Promise<AiPrompt> {
    const { call } = useAPICall()
    return await call<AiPrompt>(`/api/admin/ai-prompts/${promptType}`, {
      method: 'PUT',
      body: data,
    })
  },
}
