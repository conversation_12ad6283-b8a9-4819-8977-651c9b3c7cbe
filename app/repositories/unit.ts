import type {
  CreateUnitData,
  DuplicateUnitData,
  MoveUnitData,
  ReorderUnitData,
  Unit,
  UnitListQuery,
  UnitListResponse,
  UpdateUnitData,
} from '@/types/course'

/**
 * Unit Management Repository
 * Pure API calls for unit management endpoints
 * Handles both admin and public unit operations
 */
export const unitRepository = {
  /**
   * List all units (Admin)
   * @param query - Query parameters for filtering and pagination
   * @returns Promise resolving to paginated unit list
   */
  async listUnits(query?: UnitListQuery): Promise<UnitListResponse> {
    const { call } = useAPICall()
    return await call<UnitListResponse>('/api/admin/units', {
      method: 'GET',
      query,
    })
  },

  /**
   * Get units by course (Public)
   * @param courseId - Course ID
   * @returns Promise resolving to unit list
   */
  async getUnitsByCourse(courseId: number): Promise<UnitListResponse> {
    const { call } = useAPICall()
    return await call<UnitListResponse>(`/api/public/courses/${courseId}/units`, {
      method: 'GET',
    })
  },

  /**
   * Create a new unit (Admin)
   * @param unitData - Unit creation data
   * @returns Promise resolving to created unit
   */
  async createUnit(unitData: CreateUnitData): Promise<Unit> {
    const { call } = useAPICall()
    return await call<Unit>('/api/admin/units', {
      method: 'POST',
      body: unitData,
    })
  },

  /**
   * Get unit details (Admin)
   * @param id - Unit ID
   * @returns Promise resolving to unit details
   */
  async getUnit(id: number): Promise<Unit> {
    const { call } = useAPICall()
    return await call<Unit>(`/api/admin/units/${id}`, {
      method: 'GET',
    })
  },

  /**
   * Get unit details (Public)
   * @param id - Unit ID
   * @returns Promise resolving to unit details
   */
  async getPublicUnit(id: number): Promise<Unit> {
    const { call } = useAPICall()
    return await call<Unit>(`/api/public/units/${id}`, {
      method: 'GET',
    })
  },

  /**
   * Update unit (Admin)
   * @param id - Unit ID
   * @param unitData - Unit update data
   * @returns Promise resolving to updated unit
   */
  async updateUnit(id: number, unitData: UpdateUnitData): Promise<Unit> {
    const { call } = useAPICall()
    return await call<Unit>(`/api/admin/units/${id}`, {
      method: 'PUT',
      body: unitData,
    })
  },

  /**
   * Partially update unit (Admin)
   * @param id - Unit ID
   * @param unitData - Unit update data
   * @returns Promise resolving to updated unit
   */
  async patchUnit(id: number, unitData: UpdateUnitData): Promise<Unit> {
    const { call } = useAPICall()
    return await call<Unit>(`/api/admin/units/${id}`, {
      method: 'PATCH',
      body: unitData,
    })
  },

  /**
   * Delete unit (Admin)
   * @param id - Unit ID
   * @returns Promise resolving to deletion response
   */
  async deleteUnit(id: number): Promise<void> {
    const { call } = useAPICall()
    return await call<void>(`/api/admin/units/${id}`, {
      method: 'DELETE',
    })
  },

  /**
   * Duplicate unit (Admin)
   * @param id - Unit ID to duplicate
   * @param duplicateData - Duplication parameters
   * @returns Promise resolving to duplicated unit
   */
  async duplicateUnit(id: number, duplicateData: DuplicateUnitData): Promise<Unit> {
    const { call } = useAPICall()
    return await call<Unit>(`/api/admin/units/${id}/duplicate`, {
      method: 'POST',
      body: duplicateData,
    })
  },

  /**
   * Move unit to different course (Admin)
   * @param id - Unit ID
   * @param moveData - Move parameters
   * @returns Promise resolving to moved unit
   */
  async moveUnitToCourse(id: number, moveData: MoveUnitData): Promise<Unit> {
    const { call } = useAPICall()
    return await call<Unit>(`/api/admin/units/${id}/move-to-course`, {
      method: 'PATCH',
      body: moveData,
    })
  },

  /**
   * Reorder unit (Admin)
   * @param id - Unit ID
   * @param reorderData - Reorder parameters
   * @returns Promise resolving to reordered unit
   */
  async reorderUnit(id: number, reorderData: ReorderUnitData): Promise<Unit> {
    const { call } = useAPICall()
    return await call<Unit>(`/api/admin/units/${id}/reorder`, {
      method: 'PATCH',
      body: reorderData,
    })
  },
}
