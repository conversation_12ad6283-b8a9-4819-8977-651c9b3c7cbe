<script setup lang="ts">
import { Clock } from 'lucide-vue-next'

const route = useRoute()
const _questionId = route.params.id as string

// Reactive state
const selectedAnswer = ref<string | null>(null)
const timeRemaining = ref('20:03')

// Progress items data
const progressItems = [
  { number: 93, completed: false, current: true },
  { number: 94, completed: true, current: false },
  { number: 95, completed: true, current: false },
  { number: 96, completed: true, current: false },
  { number: 97, completed: true, current: false },
  { number: 98, completed: true, current: false },
  { number: 99, completed: true, current: false },
  { number: 100, completed: true, current: false },
]

// Answer options data
const answers = [
  { id: 'A', text: 'bigger', color: 'bg-red-500' },
  { id: 'B', text: 'big', color: 'bg-yellow-500' },
  { id: 'C', text: 'more bigger', color: 'bg-blue-500' },
  { id: 'D', text: 'more big', color: 'bg-green-500' },
]

// Handle submit function
function handleSubmit() {
  if (selectedAnswer.value) {
    // Add your submit logic here
    // You might want to navigate to next question or show results
  }
}

// Timer functionality
onMounted(() => {
  // Add timer countdown logic if needed
  // const timer = setInterval(() => {
  //   // Update timeRemaining.value
  // }, 1000)

  // onUnmounted(() => {
  //   clearInterval(timer)
  // })
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-amber-50 to-orange-100 p-4">
    <!-- Wooden frame border -->
    <div class="max-w-6xl mx-auto bg-gradient-to-br from-amber-800 to-amber-900 p-3 rounded-2xl shadow-2xl">
      <div class="bg-white rounded-xl overflow-hidden">
        <!-- Header with progress bar -->
        <div class="bg-gradient-to-r from-amber-100 to-orange-100 p-4">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-2">
              <div class="w-8 h-8 bg-green-500 rounded flex items-center justify-center">
                <span class="text-white text-xl">▶</span>
              </div>
              <div class="w-8 h-8 bg-cyan-400 rounded" />
            </div>
            <div class="flex items-center space-x-1">
              <div
                v-for="item in progressItems"
                :key="item.number"
                class="px-3 py-1 rounded-lg text-white font-bold text-sm"
                :class="item.current ? 'bg-red-500' : item.completed ? 'bg-cyan-500' : 'bg-gray-300'"
              >
                {{ item.number }}
              </div>
            </div>
            <div class="w-8 h-8 bg-green-500 rounded flex items-center justify-center">
              <span class="text-white text-xl">▶</span>
            </div>
          </div>
        </div>

        <div class="flex">
          <!-- Main content area -->
          <div class="flex-1 p-6">
            <!-- Question area with chalkboard background -->
            <div class="bg-gradient-to-br from-slate-700 to-slate-800 rounded-xl p-8 mb-6 min-h-[200px] flex items-center justify-center relative">
              <div class="absolute inset-0 bg-gradient-to-br from-slate-600/20 to-slate-900/20 rounded-xl" />
              <div class="relative text-center">
                <p class="text-white text-2xl font-light leading-relaxed">
                  The square in Ha Noi is <span class="inline-block w-32 h-1 bg-white mx-2 align-middle" />{" "}
                  than the square in Hoi
                </p>
                <p class="text-white text-2xl font-light mt-2">
                  An.
                </p>
              </div>
            </div>

            <!-- Answer options -->
            <div class="grid grid-cols-2 gap-4">
              <button
                v-for="answer in answers"
                :key="answer.id"
                class="flex items-center p-4 rounded-xl transition-all duration-200 hover:scale-105"
                :class="selectedAnswer === answer.id ? 'ring-4 ring-blue-400 shadow-lg' : 'hover:shadow-md'"
                @click="selectedAnswer = answer.id"
              >
                <div
                  class="w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold text-xl mr-4"
                  :class="answer.color"
                >
                  {{ answer.id }}
                </div>
                <span class="text-gray-700 text-xl font-medium">{{ answer.text }}</span>
              </button>
            </div>
          </div>

          <!-- Sidebar -->
          <div class="w-80 bg-gradient-to-b from-gray-100 to-gray-200 p-6 flex flex-col">
            <!-- User info -->
            <div class="bg-gray-400 rounded-lg p-4 mb-6 text-white">
              <div class="flex items-center justify-between mb-2">
                <Clock class="w-5 h-5" />
                <span class="font-bold">{{ timeRemaining }}</span>
              </div>
              <div class="text-sm">
                <p class="font-semibold">
                  Nguyễn Phú Hải
                </p>
                <p class="opacity-90">
                  ID: 1299897130
                </p>
                <p class="mt-2 font-semibold">
                  Level 1
                </p>
              </div>
              <div class="flex space-x-1 mt-2">
                <div class="w-6 h-2 bg-white/30 rounded" />
                <div class="w-6 h-2 bg-white/30 rounded" />
                <div class="w-6 h-2 bg-white/30 rounded" />
                <div class="w-6 h-2 bg-white/30 rounded" />
              </div>
            </div>

            <!-- Submit button -->
            <Button
              class="bg-red-500 hover:bg-red-600 text-white font-bold py-4 px-8 rounded-xl text-lg mb-6"
              :disabled="!selectedAnswer"
              @click="handleSubmit"
            >
              SUBMIT
            </Button>

            <!-- Globe illustration -->
            <div class="flex-1 flex items-end justify-center">
              <div class="relative">
                <div class="w-48 h-48 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center shadow-lg">
                  <div class="w-40 h-40 bg-gradient-to-br from-green-300 to-blue-400 rounded-full relative overflow-hidden">
                    <!-- Continents -->
                    <div class="absolute top-4 left-6 w-8 h-6 bg-green-600 rounded-full opacity-80" />
                    <div class="absolute top-8 right-4 w-12 h-8 bg-green-600 rounded-full opacity-80" />
                    <div class="absolute bottom-6 left-8 w-10 h-6 bg-green-600 rounded-full opacity-80" />
                    <div class="absolute bottom-4 right-6 w-6 h-4 bg-green-600 rounded-full opacity-80" />
                    <!-- Clouds -->
                    <div class="absolute top-2 left-12 w-4 h-2 bg-white rounded-full opacity-60" />
                    <div class="absolute top-6 right-8 w-3 h-2 bg-white rounded-full opacity-60" />
                  </div>
                </div>
                <!-- Stand -->
                <div class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-8 bg-gradient-to-b from-amber-600 to-amber-800 rounded-b-lg" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
