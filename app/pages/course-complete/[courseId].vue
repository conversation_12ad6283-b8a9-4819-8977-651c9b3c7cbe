<script setup lang="ts">
import { Award, BookOpen, Clock, Star, Trophy, Zap } from 'lucide-vue-next'
import { courseRepository } from '@/repositories/course'
import { useProgressStore } from '@/stores/progress'

// Route parameters
const route = useRoute()
const courseId = ref(Number(route.params.courseId))

// Stores
const progressStore = useProgressStore()

// Fetch course data
const { data: course } = await useAsyncData(
  `course-${courseId.value}`,
  () => courseRepository.getPublicCourse(courseId.value),
  {
    server: true,
    default: () => null,
  },
)

// Fetch all courses for recommendations
const { data: allCoursesData } = await useAsyncData(
  'all-courses',
  () => courseRepository.getPublicCoursesWithStats(),
  {
    server: true,
    default: () => ({ data: [], meta: { total: 0, per_page: 10, current_page: 1, last_page: 1 } }),
  },
)

const allCourses = computed(() => allCoursesData.value || [])

// Get course progress
const courseProgress = computed(() => progressStore.getCourseProgress(courseId.value))
const gameStats = computed(() => progressStore.gameStats)

// Get recommended courses (not completed)
const recommendedCourses = computed(() => {
  return allCourses.value
    .filter(c => c.id !== courseId.value && !progressStore.isCourseCompleted(c.id))
    .slice(0, 3)
})

// Calculate achievements
const achievements = computed(() => {
  const achievements = []

  if (courseProgress.value) {
    const accuracy = courseProgress.value.totalQuestions > 0
      ? Math.round((courseProgress.value.totalCorrectAnswers / courseProgress.value.totalQuestions) * 100)
      : 0

    if (accuracy === 100) {
      achievements.push({ icon: Star, text: 'Perfect Score!', color: 'text-yellow-600' })
    }
    else if (accuracy >= 90) {
      achievements.push({ icon: Award, text: 'Excellent Performance!', color: 'text-blue-600' })
    }
    else if (accuracy >= 80) {
      achievements.push({ icon: Trophy, text: 'Great Job!', color: 'text-green-600' })
    }

    if (courseProgress.value.unitsCompleted >= 5) {
      achievements.push({ icon: Zap, text: 'Dedicated Learner!', color: 'text-purple-600' })
    }

    if (gameStats.value.totalCoursesCompleted >= 3) {
      achievements.push({ icon: BookOpen, text: 'Course Master!', color: 'text-indigo-600' })
    }
  }

  return achievements
})

// Navigate to course
function startCourse() {
  navigateTo(`/`)
}

function goHome() {
  navigateTo('/')
}

// Set page meta
useHead({
  title: computed(() => {
    if (course.value) {
      return `Course Complete - ${course.value.title}`
    }
    return 'Course Complete!'
  }),
  meta: [
    { name: 'description', content: 'Congratulations on completing your English learning course!' },
  ],
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-green-50 to-blue-100 dark:from-gray-900 dark:to-green-900">
    <div class="container mx-auto px-4 py-8">
      <!-- Celebration Header -->
      <div class="text-center mb-12">
        <div class="relative inline-block mb-6">
          <div class="w-32 h-32 mx-auto bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center animate-pulse">
            <Trophy class="w-16 h-16 text-white" />
          </div>
          <div class="absolute -top-2 -right-2 w-12 h-12 bg-green-500 rounded-full flex items-center justify-center animate-bounce">
            <Star class="w-6 h-6 text-white" />
          </div>
        </div>

        <h1 class="text-4xl md:text-5xl font-bold text-gray-800 dark:text-white mb-4">
          🎉 Course Complete!
        </h1>
        <p class="text-xl text-gray-600 dark:text-gray-300 mb-2">
          Congratulations! You've successfully completed
        </p>
        <h2 class="text-2xl font-semibold text-primary mb-6">
          {{ course?.title || 'Your Course' }}
        </h2>
      </div>

      <!-- Course Statistics -->
      <div v-if="courseProgress" class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        <!-- Units Completed -->
        <Card>
          <CardContent class="p-6 text-center">
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mx-auto mb-3">
              <BookOpen class="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div class="text-2xl font-bold text-gray-800 dark:text-white">
              {{ courseProgress.unitsCompleted }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-300">
              Units Completed
            </div>
          </CardContent>
        </Card>

        <!-- Total Score -->
        <Card>
          <CardContent class="p-6 text-center">
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Trophy class="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <div class="text-2xl font-bold text-gray-800 dark:text-white">
              {{ courseProgress.totalScore }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-300">
              Total Score
            </div>
          </CardContent>
        </Card>

        <!-- Accuracy -->
        <Card>
          <CardContent class="p-6 text-center">
            <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Star class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div class="text-2xl font-bold text-gray-800 dark:text-white">
              {{ courseProgress.totalQuestions > 0 ? Math.round((courseProgress.totalCorrectAnswers / courseProgress.totalQuestions) * 100) : 0 }}%
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-300">
              Accuracy
            </div>
          </CardContent>
        </Card>

        <!-- Questions Answered -->
        <Card>
          <CardContent class="p-6 text-center">
            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mx-auto mb-3">
              <Zap class="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
            <div class="text-2xl font-bold text-gray-800 dark:text-white">
              {{ courseProgress.totalQuestions }}
            </div>
            <div class="text-sm text-gray-600 dark:text-gray-300">
              Questions Answered
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Achievements -->
      <Card v-if="achievements.length > 0" class="mb-12">
        <CardHeader>
          <CardTitle class="text-center">
            🏆 Achievements Unlocked
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid md:grid-cols-3 gap-4">
            <div
              v-for="(achievement, index) in achievements"
              :key="index"
              class="flex items-center gap-3 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
            >
              <component
                :is="achievement.icon"
                class="w-8 h-8" :class="[achievement.color]"
              />
              <span class="font-medium text-gray-800 dark:text-white">
                {{ achievement.text }}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Overall Progress -->
      <Card class="mb-12">
        <CardHeader>
          <CardTitle class="text-center">
            📊 Your Learning Journey
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid md:grid-cols-3 gap-6 text-center">
            <div>
              <div class="text-3xl font-bold text-primary mb-2">
                {{ gameStats.totalCoursesCompleted }}
              </div>
              <div class="text-gray-600 dark:text-gray-300">
                Courses Completed
              </div>
            </div>
            <div>
              <div class="text-3xl font-bold text-primary mb-2">
                {{ gameStats.totalUnitsCompleted }}
              </div>
              <div class="text-gray-600 dark:text-gray-300">
                Total Units
              </div>
            </div>
            <div>
              <div class="text-3xl font-bold text-primary mb-2">
                {{ gameStats.averageScore }}
              </div>
              <div class="text-gray-600 dark:text-gray-300">
                Average Score
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Recommended Courses -->
      <div v-if="recommendedCourses.length > 0" class="mb-12">
        <h3 class="text-2xl font-bold text-center text-gray-800 dark:text-white mb-8">
          🚀 Continue Your Learning Journey
        </h3>
        <div class="grid md:grid-cols-3 gap-6">
          <Card
            v-for="recommendedCourse in recommendedCourses"
            :key="recommendedCourse.id"
            class="hover:shadow-xl transition-all duration-300 cursor-pointer group"
            @click="startCourse()"
          >
            <CardContent class="p-6">
              <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <BookOpen class="w-8 h-8 text-white" />
                </div>
                <CardTitle class="mb-2 text-left">
                  {{ recommendedCourse.title }}
                </CardTitle>
                <CardDescription class="mb-4 text-left line-clamp-3">
                  {{ recommendedCourse.description }}
                </CardDescription>

                <!-- Course Stats -->
                <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-300 mb-4">
                  <div class="flex items-center gap-1">
                    <Zap class="w-4 h-4" />
                    <span>{{ recommendedCourse.units_count || 0 }} units</span>
                  </div>
                  <div class="flex items-center gap-1">
                    <Clock class="w-4 h-4" />
                    <span>{{ recommendedCourse.assessments_count || 0 }} questions</span>
                  </div>
                </div>

                <Button class="w-full" size="lg">
                  Start Course
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="text-center">
        <div class="space-x-4">
          <Button size="lg" @click="goHome">
            <BookOpen class="w-4 h-4" />
            Browse All Courses
          </Button>
          <Button variant="outline" size="lg" @click="navigateTo('/stats')">
            <Trophy class="w-4 h-4" />
            View Statistics
          </Button>
        </div>
      </div>
    </div>
  </div>
</template>
