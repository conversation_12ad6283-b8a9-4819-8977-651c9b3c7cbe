<script setup lang="ts">
import type { StaffLoginCredentials } from '~/types/staff'

// Meta
definePageMeta({
  layout: 'auth',
  title: 'Staff Login',
})

// Composables
const { loginWithState, loading, error, reset, isAuthenticated } = useStaffAuthState()
const router = useRouter()

// Reactive state
const credentials = reactive<StaffLoginCredentials>({
  username: '',
  password: '',
})

// Computed
const canSubmit = computed(() => {
  return credentials.username.trim() !== '' && credentials.password.trim() !== ''
})

const errorMessage = computed(() => {
  if (!error.value)
    return ''

  // Handle different error types
  if (error.value.statusCode === 401) {
    return 'Invalid username or password. Please try again.'
  }

  if (error.value.statusCode === 422) {
    const errors = error.value.data?.errors
    if (errors) {
      // Return first validation error
      const firstError = Object.values(errors)[0]
      return Array.isArray(firstError) ? firstError[0] : firstError
    }
    return 'Please check your input and try again.'
  }

  if (error.value.statusCode >= 500) {
    return 'Server error. Please try again later.'
  }

  return error.value.message || 'An unexpected error occurred.'
})

// Methods
async function handleLogin() {
  if (!canSubmit.value || loading.value)
    return

  try {
    reset() // Clear any previous errors

    await loginWithState(credentials)
    await router.push('/admin/dashboard')
  }
  catch (err) {
    // Error is already handled by the composable
    console.error('Login failed:', err)
  }
}

// Redirect if already authenticated
watch(isAuthenticated, (authenticated) => {
  if (authenticated) {
    router.push('/admin/dashboard')
  }
}, { immediate: true })

// Keyboard shortcuts
onMounted(() => {
  // check to auto login
  if (isAuthenticated.value) {
    router.push('/admin/dashboard')
  }

  const handleKeyPress = (event: KeyboardEvent) => {
    if (event.key === 'Enter' && canSubmit.value && !loading.value) {
      handleLogin()
    }
  }

  document.addEventListener('keypress', handleKeyPress)

  onUnmounted(() => {
    document.removeEventListener('keypress', handleKeyPress)
  })
})
</script>

<template>
  <div class="w-full max-w-md">
    <!-- Login Card -->
    <Card class="w-full shadow-lg">
      <CardHeader class="space-y-1">
        <CardTitle class="text-2xl font-bold text-center">
          Staff Login
        </CardTitle>
        <CardDescription class="text-center">
          Enter your credentials to access the admin panel
        </CardDescription>
      </CardHeader>

      <CardContent class="space-y-4">
        <!-- Login Form -->
        <form class="space-y-4" @submit.prevent="handleLogin">
          <!-- Username Field -->
          <div class="space-y-2">
            <Label for="username">Username</Label>
            <Input
              id="username"
              v-model="credentials.username"
              type="text"
              placeholder="Enter your username"
              required
              :disabled="loading"
              class="w-full"
            />
          </div>

          <!-- Password Field -->
          <div class="space-y-2">
            <Label for="password">Password</Label>
            <Input
              id="password"
              v-model="credentials.password"
              type="password"
              placeholder="Enter your password"
              required
              :disabled="loading"
              class="w-full"
            />
          </div>

          <!-- Error Message -->
          <div v-if="error" class="text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 p-3 rounded-md border border-red-200 dark:border-red-800">
            {{ errorMessage }}
          </div>

          <!-- Login Button -->
          <Button
            type="submit"
            class="w-full"
            :disabled="loading || !canSubmit"
          >
            <div v-if="loading" class="flex items-center justify-center space-x-2">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
              <span>Signing in...</span>
            </div>
            <span v-else>Sign In</span>
          </Button>
        </form>
      </CardContent>

      <CardFooter class="text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400">
          Admin Panel Access Only
        </p>
      </CardFooter>
    </Card>

    <!-- Additional Info -->
    <div class="mt-6 text-center">
      <p class="text-xs text-gray-500 dark:text-gray-400">
        Secure staff authentication powered by Laravel Sanctum
      </p>
    </div>
  </div>
</template>
