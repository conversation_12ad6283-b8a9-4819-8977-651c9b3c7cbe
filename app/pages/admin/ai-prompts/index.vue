<script setup lang="ts">
import type { AiPrompt } from '@/types/ai-prompt'
import { Edit, MessageSquare } from 'lucide-vue-next'
import { aiPromptRepository } from '@/repositories/ai-prompt'
import EditAiPromptDialog from '~/components/admin/EditAiPromptDialog.vue'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'AI Prompts Management',
  middleware: 'auth',
})

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'AI Prompts', active: true },
  ],
  showAuthStatus: true,
})

// Query params and search
const { queryParams } = useQueryParams()
const { search, nextPage, prevPage, goToPage } = useBaseQuery()

// Local search state for debouncing
const searchQuery = ref((queryParams.value.q as string) || '')

// Watch for search input changes with debouncing
watch(searchQuery, (newValue) => {
  const timeoutId = setTimeout(() => {
    search(newValue)
  }, 300)

  return () => clearTimeout(timeoutId)
}, { immediate: false })

// Data fetching
const { data, pending: loading, error, refresh: fetchAiPrompts } = useAsyncData(
  'aiPromptsList',
  () => aiPromptRepository.getAllAiPrompts({
    per_page: 15,
    q: queryParams.value.q as string || undefined,
    page: queryParams.value.page as string || undefined,
  }),
  {
    watch: [queryParams],
  },
)

// Computed properties
const aiPrompts = computed(() => data.value?.data || [])
const pagination = computed(() => data.value)

// Edit dialog state
const showEditDialog = ref(false)
const selectedAiPrompt = ref<AiPrompt | null>(null)

// Handle edit AI prompt
function handleEditAiPrompt(aiPrompt: AiPrompt) {
  selectedAiPrompt.value = aiPrompt
  showEditDialog.value = true
}

// Handle AI prompt updated
async function handleAiPromptUpdated() {
  await fetchAiPrompts()
}

// Page meta
useHead({
  title: 'AI Prompts Management | English Learning Game Admin',
  meta: [
    { name: 'description', content: 'Manage AI prompts for the English Learning Game. Configure prompt templates and patterns for assessment generation.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          AI Prompts Management
        </h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">
          Configure AI prompt templates and patterns for assessment generation
        </p>
      </div>
    </div>

    <!-- Search -->
    <AdminSearchBar
      v-model="searchQuery"
      placeholder="Search prompts by type or content..."
      :loading="loading"
    />

    <!-- AI Prompts List -->
    <AdminCard
      title="AI Prompts"
      :description="`${pagination?.total || 0} prompts total`"
      show-header
    >
      <!-- Loading State -->
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center py-12">
        <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-6">
          <p class="text-red-700 dark:text-red-400 font-medium mb-3">
            Failed to load AI prompts
          </p>
          <Button variant="outline" class="border-red-300 text-red-700 hover:bg-red-50" @click="fetchAiPrompts">
            Try Again
          </Button>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="aiPrompts.length === 0" class="text-center py-12">
        <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-8">
          <MessageSquare class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-700 dark:text-gray-300 font-medium mb-4">
            No AI prompts found
          </p>
          <p class="text-gray-600 dark:text-gray-400 text-sm">
            {{ searchQuery ? 'Try adjusting your search criteria' : 'No AI prompts are configured yet' }}
          </p>
        </div>
      </div>

      <!-- AI Prompts Table -->
      <div v-else class="overflow-x-auto">
        <table class="w-full">
          <thead>
            <tr class="border-b border-gray-200 dark:border-gray-700">
              <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                Prompt Type
              </th>
              <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                Prompt Temperature
              </th>
              <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                Prompt Max Tokens
              </th>
              <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                Last Modified
              </th>
              <th class="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            <tr
              v-for="aiPrompt in aiPrompts"
              :key="aiPrompt.id"
              class="border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors"
            >
              <!-- Prompt Type -->
              <td class="py-4 px-4">
                <div class="flex items-center gap-2">
                  <Badge variant="outline" class="bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-300">
                    {{ formatPromptType(aiPrompt.type) }}
                  </Badge>
                </div>
              </td>

              <!-- Prompt Temperature -->
              <td class="py-4 px-4 max-w-md">
                <div class="text-sm text-gray-900 dark:text-white p-2">
                  {{ aiPrompt.temperature }}
                </div>
              </td>

              <!-- Prompt Max Tokens -->
              <td class="py-4 px-4 max-w-sm">
                <div class="text-sm text-gray-700 dark:text-gray-300 p-2">
                  {{ aiPrompt.max_tokens }}
                </div>
              </td>

              <!-- Last Modified -->
              <td class="py-4 px-4">
                <div class="text-sm text-gray-600 dark:text-gray-400">
                  {{ new Date(aiPrompt.updated_at).toLocaleDateString() }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-500">
                  {{ new Date(aiPrompt.updated_at).toLocaleTimeString() }}
                </div>
              </td>

              <!-- Actions -->
              <td class="py-4 px-4">
                <Button
                  variant="ghost"
                  size="sm"
                  class="text-blue-600 hover:text-blue-700 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                  @click="handleEditAiPrompt(aiPrompt)"
                >
                  <Edit class="w-4 h-4" />
                </Button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </AdminCard>

    <!-- Pagination -->
    <AdminPagination
      v-if="pagination && pagination.total > pagination.per_page"
      :pagination="pagination"
      item-name="prompts"
      @prev-page="prevPage"
      @next-page="nextPage"
      @go-to-page="goToPage"
    />

    <!-- Edit AI Prompt Dialog -->
    <EditAiPromptDialog
      v-model:open="showEditDialog"
      :ai-prompt="selectedAiPrompt"
      @updated="handleAiPromptUpdated"
    />
  </div>
</template>
