<script setup lang="ts">
import type { Course } from '@/types/course'
import { BookOpen, Copy, Edit, MoreHorizontal, Plus, Trash2 } from 'lucide-vue-next'
import { courseRepository } from '@/repositories/course'
import CreateCourseDialog from '~/components/admin/CreateCourseDialog.vue'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'Course Management',
  middleware: 'auth',
})

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'Courses', active: true },
  ],
  showAuthStatus: true,
})

// Query params and search
const { queryParams } = useQueryParams()
const { search, nextPage, prevPage, goToPage } = useBaseQuery()

// Local search state for debouncing
const searchQuery = ref((queryParams.value.q as string) || '')

// Watch for search input changes with debouncing
watch(searchQuery, (newValue) => {
  const timeoutId = setTimeout(() => {
    search(newValue)
  }, 300)

  return () => clearTimeout(timeoutId)
}, { immediate: false })

// Data fetching
const { data, pending: loading, error, refresh: fetchCourses } = useAsyncData(
  'coursesList',
  () => courseRepository.listCourses({
    per_page: 15,
  }),
  {
    watch: [queryParams],
  },
)

// Computed properties
const courses = computed(() => data.value?.data || [])
const pagination = computed(() => data.value)

// Actions
const { call } = useAPICall()
const { open: openModal } = useModal()

async function handleDeleteCourse(course: Course) {
  const confirmed = await openModal({
    title: 'Delete Course',
    description: `Are you sure you want to delete "${course.title}"? This action cannot be undone.`,
    variant: 'destructive',
    buttons: [
      { text: 'Cancel', variant: 'outline' },
      { text: 'Delete', variant: 'destructive' },
    ],
  })

  if (confirmed) {
    try {
      await courseRepository.deleteCourse(course.id)
      await fetchCourses()
      // Show success toast
    }
    catch (error: any) {
      console.error('Failed to delete course:', error)
      // Show error toast
    }
  }
}

async function handleDuplicateCourse(course: Course) {
  try {
    await courseRepository.duplicateCourse(course.id)
    await fetchCourses()
    // Show success toast
  }
  catch (error: any) {
    console.error('Failed to duplicate course:', error)
    // Show error toast
  }
}

// Create course dialog state
const showCreateDialog = ref(false)

// Handle course creation
async function handleCourseCreated(course: any) {
  // Refresh courses list
  await fetchCourses()

  // Navigate to the course edit page
  await navigateTo(`/admin/courses/${course.id}`)
}

// Page meta
useHead({
  title: 'Course Management | English Learning Game Admin',
  meta: [
    { name: 'description', content: 'Manage courses for the English Learning Game. Create, edit, and organize learning content.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Course Management
        </h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">
          Create and manage learning courses
        </p>
      </div>
      <Button class="bg-blue-600 hover:bg-blue-700 text-white" @click="showCreateDialog = true">
        <Plus class="w-4 h-4" />
        Create Course
      </Button>
    </div>

    <!-- Search -->
    <AdminSearchBar
      v-model="searchQuery"
      placeholder="Search courses..."
      :loading="loading"
    />

    <!-- Courses List -->
    <AdminCard
      title="Courses"
      :description="`${pagination?.total || 0} courses total`"
      show-header
    >
      <!-- Loading State -->
      <div v-if="loading" class="flex items-center justify-center py-12">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
      </div>

      <!-- Error State -->
      <div v-else-if="error" class="text-center py-12">
        <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-6">
          <p class="text-red-700 dark:text-red-400 font-medium mb-3">
            Failed to load courses
          </p>
          <Button variant="outline" class="border-red-300 text-red-700 hover:bg-red-50" @click="fetchCourses">
            Try Again
          </Button>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else-if="courses.length === 0" class="text-center py-12">
        <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-8">
          <BookOpen class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-700 dark:text-gray-300 font-medium mb-4">
            No courses found
          </p>
          <Button class="bg-blue-600 hover:bg-blue-700 text-white" @click="showCreateDialog = true">
            Create Your First Course
          </Button>
        </div>
      </div>

      <!-- Courses List -->
      <div v-else class="space-y-3">
        <AdminItemCard
          v-for="course in courses"
          :key="course.id"
          :title="course.title"
          :description="course.description"
          :metadata="[
            { label: 'Units', value: course.units?.length || 0 },
            { label: 'Created', value: new Date(course.created_at).toLocaleDateString() },
          ]"
        >
          <template #actions>
            <DropdownMenu>
              <DropdownMenuTrigger as-child>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal class="w-4 h-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" class="w-48">
                <DropdownMenuItem as-child>
                  <NuxtLink :to="`/admin/courses/${course.id}`" class="flex items-center">
                    <Edit class="w-4 h-4" />
                    Edit Course
                  </NuxtLink>
                </DropdownMenuItem>
                <DropdownMenuItem @click="handleDuplicateCourse(course)">
                  <Copy class="w-4 h-4" />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  class="text-red-600 focus:text-red-600 focus:bg-red-50 dark:focus:bg-red-900/20"
                  @click="handleDeleteCourse(course)"
                >
                  <Trash2 class="w-4 h-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </template>
        </AdminItemCard>
      </div>
    </AdminCard>

    <!-- Pagination -->
    <AdminPagination
      v-if="pagination"
      :pagination="pagination"
      item-name="courses"
      @prev-page="prevPage"
      @next-page="nextPage"
    />

    <!-- Create Course Dialog -->
    <CreateCourseDialog
      v-model:open="showCreateDialog"
      @created="handleCourseCreated"
    />
  </div>
</template>
