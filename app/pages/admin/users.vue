<script setup lang="ts">
import { ChevronLeft, ChevronRight, MoreHorizontal, Plus, Search } from 'lucide-vue-next'

definePageMeta({
  layout: 'dashboard',
  middleware: 'admin-auth',
})

const searchQuery = ref('')
const statusFilter = ref('all')

// Mock data - replace with actual API calls
const users = ref([
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'student',
    status: 'active',
    lastActive: '2 hours ago',
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'teacher',
    status: 'active',
    lastActive: '1 day ago',
  },
  {
    id: 3,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'student',
    status: 'inactive',
    lastActive: '1 week ago',
  },
  {
    id: 4,
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    lastActive: '5 minutes ago',
  },
])

const filteredUsers = computed(() => {
  let filtered = users.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(user =>
      user.name.toLowerCase().includes(query)
      || user.email.toLowerCase().includes(query),
    )
  }

  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(user => user.status === statusFilter.value)
  }

  return filtered
})

function getStatusVariant(status: string) {
  switch (status) {
    case 'active':
      return 'default'
    case 'inactive':
      return 'secondary'
    case 'suspended':
      return 'destructive'
    default:
      return 'secondary'
  }
}
</script>

<template>
  <div class="space-y-6">
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold tracking-tight">
          Users
        </h1>
        <p class="text-muted-foreground">
          Manage platform users and their permissions
        </p>
      </div>
      <Button>
        <Plus class="w-4 h-4" />
        Add User
      </Button>
    </div>

    <AdminCard>
      <div class="space-y-4">
        <div class="flex items-center space-x-4">
          <div class="relative flex-1">
            <Search class="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input v-model="searchQuery" placeholder="Search users..." class="pl-8" />
          </div>
          <Select v-model="statusFilter">
            <SelectTrigger class="w-40">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                All Users
              </SelectItem>
              <SelectItem value="active">
                Active
              </SelectItem>
              <SelectItem value="inactive">
                Inactive
              </SelectItem>
              <SelectItem value="suspended">
                Suspended
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div class="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Last Active</TableHead>
                <TableHead class="text-right">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow v-for="user in filteredUsers" :key="user.id">
                <TableCell class="font-medium">
                  {{ user.name }}
                </TableCell>
                <TableCell>{{ user.email }}</TableCell>
                <TableCell>
                  <Badge :variant="user.role === 'admin' ? 'destructive' : 'secondary'">
                    {{ user.role }}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge :variant="getStatusVariant(user.status)">
                    {{ user.status }}
                  </Badge>
                </TableCell>
                <TableCell>{{ user.lastActive }}</TableCell>
                <TableCell class="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger as-child>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal class="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem>View Profile</DropdownMenuItem>
                      <DropdownMenuItem>Edit User</DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem class="text-destructive">
                        Suspend User
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        <div class="flex items-center justify-between">
          <p class="text-sm text-muted-foreground">
            Showing {{ filteredUsers.length }} of {{ users.length }} users
          </p>
          <div class="flex items-center space-x-2">
            <Button variant="outline" size="sm" :disabled="true">
              <ChevronLeft class="w-4 h-4" />
              Previous
            </Button>
            <Button variant="outline" size="sm" :disabled="true">
              Next
              <ChevronRight class="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    </AdminCard>
  </div>
</template>
