<script setup lang="ts">
import { Bar<PERSON>hart3, <PERSON>O<PERSON>, Clock, TrendingUp, Trophy, Users } from 'lucide-vue-next'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'Dashboard',
  middleware: 'auth',
})

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', active: true },
  ],
  showAuthStatus: true,
})

// Sample dashboard data - in real app this would come from API
const dashboardStats = ref({
  totalUsers: 1247,
  activeGames: 89,
  completedLevels: 3456,
  averageScore: 87.5,
  totalQuestions: 2340,
  dailyActiveUsers: 156,
})

const recentActivity = ref([
  {
    id: 1,
    user: '<PERSON>',
    action: 'Completed Stage 3',
    score: 950,
    time: '2 minutes ago',
  },
  {
    id: 2,
    user: '<PERSON>',
    action: 'Started new game',
    score: 0,
    time: '5 minutes ago',
  },
  {
    id: 3,
    user: '<PERSON>',
    action: 'Achieved perfect score',
    score: 1000,
    time: '10 minutes ago',
  },
  {
    id: 4,
    user: '<PERSON>',
    action: 'Completed Stage 1',
    score: 780,
    time: '15 minutes ago',
  },
])

// Set page meta
useHead({
  title: 'Dashboard | English Learning Game Admin',
  meta: [
    { name: 'description', content: 'Admin dashboard for the English Learning Game. Monitor user activity, game statistics, and system performance.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Page Header -->
    <div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
        Dashboard
      </h1>
      <p class="text-gray-600 dark:text-gray-300 mt-2">
        Welcome to the English Learning Game admin dashboard
      </p>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- Total Users -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <CardDescription>Total Users</CardDescription>
              <CardTitle class="text-2xl">
                {{ dashboardStats.totalUsers.toLocaleString() }}
              </CardTitle>
            </div>
            <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
              <Users class="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Active Games -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <CardDescription>Active Games</CardDescription>
              <CardTitle class="text-2xl">
                {{ dashboardStats.activeGames }}
              </CardTitle>
            </div>
            <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
              <BookOpen class="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Completed Levels -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <CardDescription>Completed Levels</CardDescription>
              <CardTitle class="text-2xl">
                {{ dashboardStats.completedLevels.toLocaleString() }}
              </CardTitle>
            </div>
            <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
              <Trophy class="w-6 h-6 text-purple-600 dark:text-purple-400" />
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Average Score -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <CardDescription>Average Score</CardDescription>
              <CardTitle class="text-2xl">
                {{ dashboardStats.averageScore }}%
              </CardTitle>
            </div>
            <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
              <TrendingUp class="w-6 h-6 text-orange-600 dark:text-orange-400" />
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Total Questions -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <CardDescription>Total Questions</CardDescription>
              <CardTitle class="text-2xl">
                {{ dashboardStats.totalQuestions.toLocaleString() }}
              </CardTitle>
            </div>
            <div class="w-12 h-12 bg-indigo-100 dark:bg-indigo-900 rounded-lg flex items-center justify-center">
              <BarChart3 class="w-6 h-6 text-indigo-600 dark:text-indigo-400" />
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Daily Active Users -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div>
              <CardDescription>Daily Active Users</CardDescription>
              <CardTitle class="text-2xl">
                {{ dashboardStats.dailyActiveUsers }}
              </CardTitle>
            </div>
            <div class="w-12 h-12 bg-teal-100 dark:bg-teal-900 rounded-lg flex items-center justify-center">
              <Clock class="w-6 h-6 text-teal-600 dark:text-teal-400" />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Recent Activity -->
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>Latest user actions and achievements</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div
            v-for="activity in recentActivity"
            :key="activity.id"
            class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg"
          >
            <div class="flex-1">
              <p class="font-medium text-gray-900 dark:text-white">
                {{ activity.user }}
              </p>
              <p class="text-sm text-gray-600 dark:text-gray-300">
                {{ activity.action }}
              </p>
            </div>
            <div class="text-right">
              <p class="font-medium text-gray-900 dark:text-white">
                {{ activity.score > 0 ? `${activity.score} pts` : '-' }}
              </p>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ activity.time }}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
