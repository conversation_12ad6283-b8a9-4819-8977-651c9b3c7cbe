<script setup lang="ts">
import { Lightbulb, Loader2, Play } from 'lucide-vue-next'
import { getAiAssessmentTypeOptions } from '@/helpers/assessment'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'AI Assessment Testing',
  middleware: 'auth',
})

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'AI Assessment Testing', active: true },
  ],
  showAuthStatus: true,
})

// Available assessment types for testing
const assessmentTypes = await getAiAssessmentTypeOptions()

// Composables
const assessmentState = useAiAssessmentState()
const gapFilling = useGapFilling()
const assessmentTest = useAiAssessmentTest()

// Destructure composable returns for easier access
const {
  selectedType,
  selectedQuestion,
  loadingQuestions,
  error,
  questions,
  testResult,
  fetchQuestions,
  clearAll: clearAssessmentState,
  setTestResult,
  setError,
} = assessmentState

const {
  gapAnswers,
  questionParts,
  allGapsFilled,
  parseQuestionForGaps,
  resetAnswerForm,
  getGapIndex,
  reconstructCompletedSentence,
  updateGapAnswer,
} = gapFilling

const { loadingTest, submitTest: executeTest } = assessmentTest

// Watch for type selection changes
watch(selectedType, async (newType) => {
  if (newType) {
    await fetchQuestions(newType)
    resetAnswerForm()
  }
  else {
    resetAnswerForm()
  }
})

// Watch for question selection changes
watch(selectedQuestion, (newQuestion) => {
  if (newQuestion) {
    parseQuestionForGaps(newQuestion.question, newQuestion.fill_position)
  }
  else {
    resetAnswerForm()
  }
})

// Submit test with composable
async function submitTest() {
  if (!selectedQuestion.value || !allGapsFilled.value)
    return

  const completedSentence = reconstructCompletedSentence(selectedQuestion.value)

  await executeTest(
    selectedQuestion.value,
    completedSentence,
    result => setTestResult(result),
    errorMessage => setError(errorMessage),
  )
}

// Clear all selections and start over
function clearAll() {
  clearAssessmentState()
  resetAnswerForm()
}

// Page meta
useHead({
  title: 'AI Assessment Testing | English Learning Game Admin',
  meta: [
    { name: 'description', content: 'Test AI assessment prompts and responses for the English Learning Game.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          AI Assessment Testing
        </h1>
        <p class="text-gray-600 dark:text-gray-300 mt-2">
          Test AI assessment prompts and responses without affecting production data
        </p>
      </div>

      <Button v-if="selectedType || selectedQuestion" variant="outline" @click="clearAll">
        Clear All
      </Button>
    </div>

    <!-- Error Alert -->
    <Alert v-if="error" variant="destructive">
      <AlertTitle>Error</AlertTitle>
      <AlertDescription>{{ error }}</AlertDescription>
    </Alert>

    <!-- Step 1: Assessment Type Selection -->
    <AdminCard
      title="Step 1: Select Assessment Type"
      description="Choose the type of AI assessment you want to test"
      show-header
    >
      <div class="space-y-4">
        <Label for="assessment-type">Assessment Type</Label>
        <Select v-model="selectedType">
          <SelectTrigger class="w-full max-w-sm">
            <SelectValue placeholder="Choose an assessment type..." />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="type in assessmentTypes"
              :key="type.value"
              :value="type.value"
            >
              {{ type.name }}
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </AdminCard>

    <!-- Step 2: Question Selection -->
    <AdminCard
      v-if="selectedType"
      title="Step 2: Select a Question"
      :description="loadingQuestions ? 'Loading questions...' : `${questions.length} questions available`"
      show-header
    >
      <!-- Loading State -->
      <div v-if="loadingQuestions" class="flex items-center justify-center py-8">
        <Loader2 class="w-6 h-6 animate-spin mr-2" />
        <span class="text-gray-600 dark:text-gray-300">Loading questions...</span>
      </div>

      <!-- Empty State -->
      <div v-else-if="questions.length === 0" class="text-center py-8">
        <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-6">
          <Lightbulb class="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <p class="text-gray-700 dark:text-gray-300 font-medium mb-2">
            No questions found
          </p>
          <p class="text-gray-600 dark:text-gray-400 text-sm">
            No questions are available for the selected assessment type.
          </p>
        </div>
      </div>

      <!-- Questions List -->
      <div v-else class="space-y-4">
        <Label for="question-select">Available Questions</Label>
        <Select v-model="selectedQuestion">
          <SelectTrigger class="w-full">
            <SelectValue placeholder="Choose a question to test..." />
          </SelectTrigger>
          <SelectContent>
            <SelectItem
              v-for="question in questions"
              :key="question.assessment_id"
              :value="question"
            >
              <div class="flex flex-col items-start">
                <span class="font-medium">{{ question.question.substring(0, 80) }}{{ question.question.length > 80 ? '...' : '' }}</span>
                <span class="text-xs text-gray-500">Unit ID: {{ question.unit_id }} | Assessment ID: {{ question.assessment_id }}</span>
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
    </AdminCard>

    <!-- Step 3: Answer Input (Gap Filling) -->
    <AdminCard
      v-if="selectedQuestion && questionParts.length > 0"
      title="Step 3: Fill in the Gaps"
      description="Complete the sentence by filling in the missing words"
      show-header
    >
      <div class="space-y-6">
        <!-- Question Display with Interactive Gaps -->
        <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
          <h3 class="font-medium text-lg mb-4 text-gray-900 dark:text-white">
            Complete the sentence:
          </h3>
          <div class="text-lg leading-relaxed">
            <template v-for="(part, index) in questionParts" :key="index">
              <span v-if="!part.isGap" class="text-gray-800 dark:text-gray-200">{{ part.text.replaceAll('_', '') }}</span>
              <Input
                v-else
                :model-value="gapAnswers[getGapIndex(index)] || ''"
                :disabled="loadingTest"
                class="inline-block w-32 mx-1 text-center"
                placeholder="___"
                @update:model-value="updateGapAnswer(getGapIndex(index), $event as string)"
              />
            </template>
          </div>
        </div>

        <!-- Submit Button -->
        <div class="flex justify-center">
          <Button
            :disabled="!allGapsFilled || loadingTest"
            class="px-8 py-2"
            @click="submitTest"
          >
            <Loader2 v-if="loadingTest" class="w-4 h-4 animate-spin mr-2" />
            <Play v-else class="w-4 h-4 mr-2" />
            {{ loadingTest ? 'Testing...' : 'Test AI Assessment' }}
          </Button>
        </div>
      </div>
    </AdminCard>

    <!-- Step 4: Test Results -->
    <AdminCard
      v-if="testResult"
      title="Test Results"
      description="AI assessment results and detailed information"
      show-header
    >
      <div class="space-y-6">
        <!-- AI Output -->
        <div v-if="testResult.ai_output.length > 0">
          <h3 class="font-medium text-lg mb-4 text-gray-900 dark:text-white">
            AI Assessment Results
          </h3>
          <div class="space-y-4">
            <div
              v-for="result in testResult.ai_output"
              :key="result.id"
              class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4"
            >
              <div class="flex items-center justify-between mb-2">
                <Badge variant="outline" class="bg-blue-50 dark:bg-blue-900/30 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-300">
                  Assessment {{ result.id }}
                </Badge>
                <Badge :variant="result.point.includes('10') || result.point.includes('9') ? 'default' : result.point.includes('7') || result.point.includes('8') ? 'secondary' : 'destructive'">
                  {{ result.point }}
                </Badge>
              </div>
              <p class="text-gray-700 dark:text-gray-300">
                {{ result.comment }}
              </p>
            </div>
          </div>
        </div>

        <!-- Model Information -->
        <div>
          <h3 class="font-medium text-lg mb-4 text-gray-900 dark:text-white">
            Model Information
          </h3>
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {{ testResult.model_info.model_name }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                Model
              </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                {{ testResult.model_info.processing_time_ms }}ms
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                Processing Time
              </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                {{ testResult.model_info.input_tokens }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                Input Tokens
              </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 text-center">
              <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {{ testResult.model_info.output_tokens }}
              </div>
              <div class="text-sm text-gray-600 dark:text-gray-400">
                Output Tokens
              </div>
            </div>
          </div>
        </div>

        <!-- Final Prompt (Collapsible) -->
        <details class="bg-gray-50 dark:bg-gray-900 rounded-lg">
          <summary class="cursor-pointer p-4 font-medium text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg">
            View Full AI Prompt
          </summary>
          <div class="p-4 pt-0">
            <pre class="whitespace-pre-wrap text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded p-4 overflow-x-auto">{{ testResult.final_prompt }}</pre>
          </div>
        </details>
      </div>
    </AdminCard>
  </div>
</template>
