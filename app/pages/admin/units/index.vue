<script setup lang="ts">
import type { AcceptableValue } from 'reka-ui'
import type { Difficulty, SkillType, Unit } from '@/types/course'
import { difficultyOptions, skillTypeOptions } from '#shared/constants'
import { BookOpen, Copy, Edit, MoreHorizontal, Plus, Search, Trash2 } from 'lucide-vue-next'
import { computed, ref, watch } from 'vue'
import CreateUnitDialog from '@/components/admin/CreateUnitDialog.vue'
import { courseRepository } from '@/repositories/course'
import { unitRepository } from '@/repositories/unit'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'Unit Management',
  middleware: 'auth',
})

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'Units', active: true },
  ],
  showAuthStatus: true,
})

// Query params and search
const { queryParams, setQueryParam } = useQueryParams()
const { search, nextPage, prevPage } = useBaseQuery()

// Local search state for debouncing
const searchQuery = ref((queryParams.value.q as string) || '')

// Filter options
const skillTypeOptionsWithAll = [
  { value: 'all', label: 'All Skill Types' },
  ...skillTypeOptions,
]

const difficultyOptionsWithAll = [
  { value: 'all', label: 'All Difficulties' },
  ...difficultyOptions,
]

// Watch for search input changes with debouncing
watch(searchQuery, (newValue) => {
  const timeoutId = setTimeout(() => {
    search(newValue)
  }, 300)

  return () => clearTimeout(timeoutId)
}, { immediate: false })

// Data fetching
const { data, pending: loading, error, refresh: fetchUnits } = useAsyncData(
  'unitsList',
  () => {
    const params: any = { per_page: 15, page: queryParams.value.page ?? 1 }

    if (queryParams.value.course_id) {
      params.course_id = Number(queryParams.value.course_id)
    }
    if (queryParams.value.skill_type && queryParams.value.skill_type !== 'all') {
      params.skill_type = queryParams.value.skill_type as SkillType
    }
    if (queryParams.value.difficulty && queryParams.value.difficulty !== 'all') {
      params.difficulty = queryParams.value.difficulty as Difficulty
    }

    return unitRepository.listUnits(params)
  },
  {
    watch: [queryParams],
  },
)

// Fetch courses for filter
const { data: coursesData } = useAsyncData(
  'coursesForFilter',
  () => courseRepository.listCourses({ per_page: 100 }),
)

// Computed properties
const units = computed(() => data.value?.data || [])
const pagination = computed(() => data.value)
const courses = computed(() => coursesData.value?.data || [])

// Filter handlers
function handleSkillTypeFilter(skillType: AcceptableValue) {
  const newSkillType = skillType === 'all' ? undefined : String(skillType)
  setQueryParam({ skill_type: newSkillType, page: undefined })
}

function handleDifficultyFilter(difficulty: AcceptableValue) {
  const newDifficulty = difficulty === 'all' ? undefined : String(difficulty)
  setQueryParam({ difficulty: newDifficulty, page: undefined })
}

function handleCourseFilter(courseId: AcceptableValue) {
  const newCourseId = courseId === 'all' ? undefined : String(courseId)
  setQueryParam({ course_id: newCourseId, page: undefined })
}

// Actions
const { confirm } = useModal()
const showCreateDialog = ref(false)

function handleCreateUnit() {
  showCreateDialog.value = true
}

function handleUnitCreated(_unit: Unit) {
  fetchUnits()
}

async function handleDeleteUnit(unit: Unit) {
  const confirmed = await confirm({
    title: 'Delete Unit',
    description: `Are you sure you want to delete "${unit.title}"? This action cannot be undone.`,
    variant: 'destructive',
  })

  if (confirmed) {
    try {
      await unitRepository.deleteUnit(unit.id)
      await fetchUnits()
      // Show success toast
    }
    catch (error: any) {
      console.error('Failed to delete unit:', error)
      // Show error toast
    }
  }
}

async function handleDuplicateUnit(unit: Unit) {
  try {
    await unitRepository.duplicateUnit(unit.id, { target_order: unit.unit_order + 1 })
    await fetchUnits()
    // Show success toast
  }
  catch (error: any) {
    console.error('Failed to duplicate unit:', error)
    // Show error toast
  }
}

// Page meta
useHead({
  title: 'Unit Management | English Learning Game Admin',
  meta: [
    { name: 'description', content: 'Manage learning units for the English Learning Game. Create, edit, and organize course content.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Page Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
          Unit Management
        </h1>
        <p class="text-gray-700 dark:text-gray-300 mt-2 font-medium">
          Create and manage learning units
        </p>
      </div>
      <Button
        class="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
        @click="handleCreateUnit"
      >
        <Plus class="w-4 h-4" />
        Create Unit
      </Button>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
      <div class="p-6">
        <div class="flex flex-col lg:flex-row items-start lg:items-center gap-4">
          <!-- Search -->
          <div class="relative flex-1">
            <Search class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400 w-4 h-4" />
            <Input
              v-model="searchQuery"
              placeholder="Search units..."
              class="pl-10 bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
            />
          </div>

          <!-- Filters -->
          <div class="flex flex-wrap items-center gap-3">
            <!-- Course Filter -->
            <div class="min-w-[180px]">
              <Select
                :model-value="(queryParams.course_id as string) || 'all'"
                @update:model-value="handleCourseFilter"
              >
                <SelectTrigger class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100">
                  <SelectValue placeholder="All Courses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    All Courses
                  </SelectItem>
                  <SelectItem
                    v-for="course in courses"
                    :key="course.id"
                    :value="course.id.toString()"
                  >
                    {{ course.title }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- Skill Type Filter -->
            <div class="min-w-[160px]">
              <Select
                :model-value="(queryParams.skill_type as string) || 'all'"
                @update:model-value="handleSkillTypeFilter"
              >
                <SelectTrigger class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100">
                  <SelectValue placeholder="All Skills" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="option in skillTypeOptionsWithAll"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- Difficulty Filter -->
            <div class="min-w-[160px]">
              <Select
                :model-value="(queryParams.difficulty as string) || 'all'"
                @update:model-value="handleDifficultyFilter"
              >
                <SelectTrigger class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100">
                  <SelectValue placeholder="All Levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem
                    v-for="option in difficultyOptionsWithAll"
                    :key="option.value"
                    :value="option.value"
                  >
                    {{ option.label }}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Units Table -->
    <div class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
          Units
        </h2>
        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
          {{ pagination?.total || 0 }} units total
        </p>
      </div>
      <div class="p-6">
        <div v-if="loading" class="flex items-center justify-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
        </div>

        <div v-else-if="error" class="text-center py-12">
          <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-6">
            <p class="text-red-700 dark:text-red-400 font-medium mb-3">
              Failed to load units
            </p>
            <Button variant="outline" class="border-red-300 text-red-700 hover:bg-red-50" @click="fetchUnits">
              Try Again
            </Button>
          </div>
        </div>

        <div v-else-if="units.length === 0" class="text-center py-12">
          <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-8">
            <BookOpen class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p class="text-gray-700 dark:text-gray-300 font-medium mb-4">
              No units found
            </p>
            <Button
              class="bg-blue-600 hover:bg-blue-700 text-white"
              @click="handleCreateUnit"
            >
              Create Your First Unit
            </Button>
          </div>
        </div>

        <div v-else class="space-y-3">
          <div
            v-for="unit in units"
            :key="unit.id"
            class="group bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-5 hover:bg-white dark:hover:bg-gray-800 hover:shadow-md transition-all duration-200"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1 min-w-0">
                <div class="flex items-center gap-3 mb-3">
                  <h3 class="font-semibold text-gray-900 dark:text-white text-lg truncate">
                    {{ unit.title }}
                  </h3>
                  <Badge
                    variant="secondary"
                    class="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 font-medium"
                  >
                    {{ unit.skill_type }}
                  </Badge>
                  <Badge
                    variant="outline"
                    class="border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300 font-medium"
                  >
                    {{ unit.difficulty }}
                  </Badge>
                </div>
                <p class="text-gray-700 dark:text-gray-300 mb-3 leading-relaxed">
                  {{ unit.description }}
                </p>
                <div class="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
                  <span class="flex items-center gap-1">
                    <span class="font-medium">Course:</span>
                    {{ unit.course?.title }}
                  </span>
                  <span class="flex items-center gap-1">
                    <span class="font-medium">Order:</span>
                    {{ unit.unit_order }}
                  </span>
                  <span class="flex items-center gap-1">
                    <span class="font-medium">Assessments:</span>
                    {{ unit.assessments?.length || 0 }}
                  </span>
                </div>
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger as-child>
                  <Button variant="ghost" size="sm" class="opacity-0 group-hover:opacity-100 transition-opacity">
                    <MoreHorizontal class="w-4 h-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" class="w-48">
                  <DropdownMenuItem as-child>
                    <NuxtLink :to="`/admin/units/${unit.id}`" class="flex items-center">
                      <Edit class="w-4 h-4" />
                      Edit Unit
                    </NuxtLink>
                  </DropdownMenuItem>
                  <DropdownMenuItem @click="handleDuplicateUnit(unit)">
                    <Copy class="w-4 h-4" />
                    Duplicate
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    class="text-red-600 focus:text-red-600 focus:bg-red-50 dark:focus:bg-red-900/20"
                    @click="handleDeleteUnit(unit)"
                  >
                    <Trash2 class="w-4 h-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Pagination -->
    <div v-if="pagination && pagination.total > pagination.per_page" class="bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm p-4">
      <div class="flex items-center justify-between">
        <p class="text-sm font-medium text-gray-700 dark:text-gray-300">
          Showing <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.from }}</span> to
          <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.to }}</span> of
          <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.total }}</span> units
        </p>

        <div class="flex items-center gap-3">
          <Button
            variant="outline"
            size="sm"
            :disabled="pagination.current_page <= 1"
            class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            @click="prevPage"
          >
            Previous
          </Button>

          <span class="text-sm font-medium text-gray-700 dark:text-gray-300 px-3">
            Page <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.current_page }}</span> of
            <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.last_page }}</span>
          </span>

          <Button
            variant="outline"
            size="sm"
            :disabled="pagination.current_page >= pagination.last_page"
            class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
            @click="nextPage"
          >
            Next
          </Button>
        </div>
      </div>
    </div>

    <!-- Create Unit Dialog -->
    <CreateUnitDialog
      v-model:open="showCreateDialog"
      @created="handleUnitCreated"
    />
  </div>
</template>
