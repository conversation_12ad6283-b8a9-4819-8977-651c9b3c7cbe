<script setup lang="ts">
import { BarChart3, <PERSON>Circle, ChevronLeft, Clock, Target } from 'lucide-vue-next'
import { ref } from 'vue'
import { useProgressStore } from '@/stores/progress'

// Progress store
const progressStore = useProgressStore()

// Get real data from store
const gameStats = computed(() => progressStore.gameStats)
const completedCourses = computed(() => progressStore.completedCourses)
const inProgressCourses = computed(() => progressStore.inProgressCourses)
const overallProgress = computed(() => progressStore.overallProgress)

// Format time helper
function formatTime(seconds: number): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (hours > 0) {
    return `${hours}h ${minutes}m`
  }
  return `${minutes}m`
}

const weeklyProgress = ref([
  { week: 'Week 1', progress: 25, score: 320 },
  { week: 'Week 2', progress: 45, score: 680 },
  { week: 'Week 3', progress: 70, score: 920 },
  { week: 'Week 4', progress: 85, score: 1240 },
  { week: 'Week 5', progress: 95, score: 1580 },
])

const stageStats = ref([
  { id: 1, title: 'Basic Grammar', completed: true, score: 850, accuracy: 94 },
  { id: 2, title: 'Vocabulary Building', completed: true, score: 920, accuracy: 89 },
  { id: 3, title: 'Sentence Structure', completed: true, score: 680, accuracy: 78 },
  { id: 4, title: 'Reading Comprehension', completed: false, score: 0, accuracy: 0 },
  { id: 5, title: 'Advanced Grammar', completed: false, score: 0, accuracy: 0 },
])

const achievements = ref([
  {
    id: 1,
    icon: '🏆',
    title: 'First Victory',
    description: 'Completed your first stage',
    date: '2 days ago',
  },
  {
    id: 2,
    icon: '🔥',
    title: 'Hot Streak',
    description: '5 correct answers in a row',
    date: '1 day ago',
  },
  {
    id: 3,
    icon: '🎯',
    title: 'Perfect Score',
    description: '100% accuracy on a level',
    date: 'Today',
  },
  {
    id: 4,
    icon: '⚡',
    title: 'Speed Demon',
    description: 'Answered 10 questions under 2 minutes',
    date: 'Today',
  },
  {
    id: 5,
    icon: '📚',
    title: 'Scholar',
    description: 'Completed 3 stages',
    date: 'Today',
  },
  {
    id: 6,
    icon: '💪',
    title: 'Persistent',
    description: 'Played for 3 consecutive days',
    date: 'Yesterday',
  },
])

// Methods
function goBack() {
  navigateTo('/')
}

function continueGame() {
  // Find next incomplete stage
  const nextStage = stageStats.value.find(stage => !stage.completed)
  if (nextStage) {
    navigateTo(`/game/stage/${nextStage.id}/level/1`)
  }
  else {
    navigateTo('/game/stage/1/level/1')
  }
}

function resetProgress() {
  if (confirm('Are you sure you want to reset all your progress? This action cannot be undone.')) {
    // Reset progress logic
    alert('Progress reset successfully!')
    // In real app, this would clear localStorage/API data
  }
}

function exportStats() {
  // Export statistics as JSON or CSV
  const data = {
    totalScore: totalScore.value,
    completedStages: completedStages.value,
    averageAccuracy: averageAccuracy.value,
    timePlayed: timePlayed.value,
    stageStats: stageStats.value,
    achievements: achievements.value,
  }

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'english-game-stats.json'
  a.click()
  URL.revokeObjectURL(url)
}

// Set page meta
useHead({
  title: 'Statistics | English Learning Game',
  meta: [
    { name: 'description', content: 'Track your English learning progress, view statistics, and monitor your achievements.' },
  ],
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-blue-900">
    <div class="container mx-auto px-4 py-8">
      <!-- Header -->
      <header class="mb-8">
        <div class="flex items-center justify-between mb-6">
          <Button
            variant="outline"
            size="sm"
            class="flex items-center gap-2"
            @click="goBack"
          >
            <ChevronLeft class="w-4 h-4" />
            Back to Menu
          </Button>
        </div>

        <div class="text-center">
          <h1 class="text-4xl md:text-5xl font-bold text-gray-800 dark:text-white mb-4">
            📊 Your Statistics
          </h1>
          <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
            Track your English learning progress and achievements
          </p>
        </div>
      </header>

      <!-- Overall Stats Cards -->
      <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <CardDescription>Total Questions Answered</CardDescription>
                <CardTitle class="text-2xl">
                  {{ gameStats.totalQuestionsAnswered.toLocaleString() }}
                </CardTitle>
              </div>
              <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                <BarChart3 class="w-6 h-6 text-blue-600 dark:text-blue-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <CardDescription>Courses Completed</CardDescription>
                <CardTitle class="text-2xl">
                  {{ gameStats.totalCoursesCompleted }}
                </CardTitle>
              </div>
              <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                <CheckCircle class="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <CardDescription>Average Accuracy</CardDescription>
                <CardTitle class="text-2xl">
                  {{ gameStats.totalQuestionsAnswered > 0 ? Math.round((gameStats.totalCorrectAnswers / gameStats.totalQuestionsAnswered) * 100) : 0 }}%
                </CardTitle>
              </div>
              <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                <Target class="w-6 h-6 text-purple-600 dark:text-purple-400" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="p-6">
            <div class="flex items-center justify-between">
              <div>
                <CardDescription>Time Played</CardDescription>
                <CardTitle class="text-2xl">
                  {{ formatTime(gameStats.totalTimeSpent) }}
                </CardTitle>
              </div>
              <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                <Clock class="w-6 h-6 text-orange-600 dark:text-orange-400" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Progress Chart and Stage Details -->
      <div class="grid lg:grid-cols-2 gap-8 mb-8">
        <!-- Progress Chart -->
        <Card>
          <CardHeader>
            <CardTitle>Progress Over Time</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div v-for="week in weeklyProgress" :key="week.week" class="flex items-center">
                <span class="text-sm font-medium text-muted-foreground w-20">{{ week.week }}</span>
                <div class="flex-1 mx-4">
                  <Progress :model-value="week.progress" class="h-3" />
                </div>
                <span class="text-sm font-medium">{{ week.score }}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Stage Breakdown -->
        <Card>
          <CardHeader>
            <CardTitle>Stage Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div class="space-y-4">
              <div v-for="stage in stageStats" :key="stage.id" class="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                <div class="flex items-center gap-3">
                  <div
                    :class="stage.completed ? 'bg-green-100 dark:bg-green-900 text-green-600 dark:text-green-400' : 'bg-muted text-muted-foreground'"
                    class="w-10 h-10 rounded-lg flex items-center justify-center font-bold"
                  >
                    {{ stage.id }}
                  </div>
                  <div>
                    <p class="font-medium">
                      Stage {{ stage.id }}
                    </p>
                    <CardDescription>{{ stage.title }}</CardDescription>
                  </div>
                </div>
                <div class="text-right">
                  <p class="font-bold">
                    {{ stage.score }}
                  </p>
                  <CardDescription>{{ stage.accuracy }}% accuracy</CardDescription>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <!-- Recent Achievements -->
      <Card class="mb-8">
        <CardHeader>
          <CardTitle>Recent Achievements</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div v-for="achievement in achievements" :key="achievement.id" class="flex items-center gap-3 p-4 rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
              <div class="text-2xl">
                {{ achievement.icon }}
              </div>
              <div>
                <p class="font-semibold">
                  {{ achievement.title }}
                </p>
                <CardDescription class="text-sm">
                  {{ achievement.description }}
                </CardDescription>
                <CardDescription class="text-xs">
                  {{ achievement.date }}
                </CardDescription>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Action Buttons -->
      <div class="text-center space-y-4 md:space-y-0 md:space-x-4 md:flex md:justify-center">
        <Button
          size="lg"
          class="w-full md:w-auto px-8"
          @click="continueGame"
        >
          Continue Learning
        </Button>

        <Button
          variant="outline"
          size="lg"
          class="w-full md:w-auto px-8"
          @click="resetProgress"
        >
          Reset Progress
        </Button>

        <Button
          variant="secondary"
          size="lg"
          class="w-full md:w-auto px-8"
          @click="exportStats"
        >
          Export Data
        </Button>
      </div>
    </div>
  </div>
</template>
