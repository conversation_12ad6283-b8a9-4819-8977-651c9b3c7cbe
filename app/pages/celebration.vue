<script setup lang="ts">
import { ArrowRight, Facebook, Home, Linkedin, RotateCcw, Sparkles, Trophy, Twitter } from 'lucide-vue-next'
import { computed, onMounted, ref } from 'vue'

// Game data - would come from route params or store in real app
const completedStage = ref(1)
const score = ref(850)
const correctAnswers = ref(8)
const totalQuestions = ref(10)

const accuracy = computed(() => Math.round((correctAnswers.value / totalQuestions.value) * 100))

const motivationalMessages = [
  'Your English skills are improving rapidly! Each question you answer correctly brings you closer to fluency.',
  'Excellent work! You\'re building a strong foundation in English. Keep up the momentum!',
  'Outstanding progress! Your dedication to learning English is truly paying off.',
  'Fantastic job! You\'re developing great English comprehension skills. Ready for the next challenge?',
  'Impressive achievement! Your English learning journey is off to a brilliant start.',
]

const motivationalMessage = computed(() => {
  const index = Math.floor(Math.random() * motivationalMessages.length)
  return motivationalMessages[index]
})

// Methods
function nextStage() {
  const nextStageNumber = completedStage.value + 1
  navigateTo(`/game/stage/${nextStageNumber}/level/1`)
}

function reviewStage() {
  navigateTo(`/game/stage/${completedStage.value}/level/1`)
}

function goHome() {
  navigateTo('/')
}

function shareAchievement(platform: string) {
  const message = `I just completed Stage ${completedStage.value} in the English Learning Game! 🎉 Score: ${score.value} points with ${accuracy.value}% accuracy. #EnglishLearning #GameBasedLearning`

  const urls = {
    twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.origin)}&quote=${encodeURIComponent(message)}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.origin)}&summary=${encodeURIComponent(message)}`,
  }

  window.open(urls[platform as keyof typeof urls], '_blank', 'width=600,height=400')
}

function getConfettiStyle(index: number) {
  const colors = ['bg-red-400', 'bg-blue-400', 'bg-green-400', 'bg-yellow-400', 'bg-purple-400', 'bg-pink-400']
  return {
    left: `${Math.random() * 100}%`,
    top: `${Math.random() * 100}%`,
    animationDelay: `${Math.random() * 2}s`,
    animationDuration: `${2 + Math.random() * 2}s`,
  }
}

function getConfettiColor(index: number) {
  const colors = ['bg-red-400', 'bg-blue-400', 'bg-green-400', 'bg-yellow-400', 'bg-purple-400', 'bg-pink-400']
  return colors[index % colors.length]
}

// Set page meta
useHead({
  title: 'Celebration! Stage Completed | English Game',
  meta: [
    { name: 'description', content: 'Congratulations on completing the stage! Celebrate your English learning achievement.' },
  ],
})

onMounted(() => {
  // Add some celebration sound or additional animations
  console.log('🎉 Celebration page loaded!')
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-yellow-50 via-orange-50 to-red-50 dark:from-gray-900 dark:via-purple-900 dark:to-blue-900 flex items-center justify-center">
    <div class="container mx-auto px-4 py-8 text-center">
      <!-- Celebration Animation Container -->
      <div class="relative max-w-2xl mx-auto">
        <!-- Confetti Animation -->
        <div class="absolute inset-0 pointer-events-none">
          <div
            v-for="i in 20"
            :key="i"
            class="absolute animate-bounce"
            :style="getConfettiStyle(i)"
          >
            <div
              class="w-3 h-3 rounded-full"
              :class="getConfettiColor(i)"
            />
          </div>
        </div>

        <!-- Main Celebration Content -->
        <Card class="relative z-10 shadow-2xl">
          <CardContent class="p-8 md:p-12">
            <!-- Trophy Icon -->
            <div class="text-center mb-8">
              <div class="w-24 h-24 mx-auto bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                <Trophy class="w-12 h-12 text-white" />
              </div>
            </div>

            <!-- Celebration Message -->
            <div class="text-center mb-8">
              <CardTitle class="text-4xl md:text-5xl mb-4 animate-bounce">
                🎉 Congratulations! 🎉
              </CardTitle>
              <CardDescription class="text-xl md:text-2xl mb-6">
                You've successfully completed Stage {{ completedStage }}!
              </CardDescription>

              <Card class="bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 mb-6">
                <CardHeader>
                  <CardTitle class="text-lg">
                    Your Achievement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div class="grid md:grid-cols-3 gap-4 text-center">
                    <div>
                      <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                        {{ score }}
                      </div>
                      <CardDescription>Points Earned</CardDescription>
                    </div>
                    <div>
                      <div class="text-2xl font-bold text-green-600 dark:text-green-400">
                        {{ correctAnswers }}/{{ totalQuestions }}
                      </div>
                      <CardDescription>Correct Answers</CardDescription>
                    </div>
                    <div>
                      <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">
                        {{ accuracy }}%
                      </div>
                      <CardDescription>Accuracy</CardDescription>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <!-- Motivational Message -->
            <Alert class="mb-8 bg-gradient-to-r from-green-100 to-blue-100 dark:from-green-900 dark:to-blue-900 border-green-500">
              <Sparkles class="h-4 w-4" />
              <AlertTitle>Keep Going!</AlertTitle>
              <AlertDescription>
                {{ motivationalMessage }}
              </AlertDescription>
            </Alert>

            <!-- Action Buttons -->
            <div class="space-y-4 md:space-y-0 md:space-x-4 md:flex md:justify-center">
              <Button
                size="lg"
                class="w-full md:w-auto px-8 py-3 flex items-center justify-center gap-2"
                @click="nextStage"
              >
                Continue to Next Stage
                <ArrowRight class="w-5 h-5" />
              </Button>

              <Button
                variant="outline"
                size="lg"
                class="w-full md:w-auto px-8 py-3 flex items-center justify-center gap-2"
                @click="reviewStage"
              >
                <RotateCcw class="w-5 h-5" />
                Review This Stage
              </Button>

              <Button
                variant="secondary"
                size="lg"
                class="w-full md:w-auto px-8 py-3 flex items-center justify-center gap-2"
                @click="goHome"
              >
                <Home class="w-5 h-5" />
                Main Menu
              </Button>
            </div>

            <!-- Share Achievement -->
            <div class="mt-8 pt-6 border-t">
              <CardDescription class="text-center mb-3">
                Share Your Achievement
              </CardDescription>
              <div class="flex justify-center space-x-3">
                <Button
                  size="icon"
                  class="w-10 h-10 bg-blue-500 hover:bg-blue-600"
                  @click="shareAchievement('twitter')"
                >
                  <Twitter class="w-5 h-5" />
                </Button>
                <Button
                  size="icon"
                  class="w-10 h-10 bg-blue-700 hover:bg-blue-800"
                  @click="shareAchievement('facebook')"
                >
                  <Facebook class="w-5 h-5" />
                </Button>
                <Button
                  size="icon"
                  class="w-10 h-10 bg-blue-600 hover:bg-blue-700"
                  @click="shareAchievement('linkedin')"
                >
                  <Linkedin class="w-5 h-5" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes confetti-fall {
  0% {
    transform: translateY(-100vh) rotate(0deg);
    opacity: 1;
  }
  100% {
    transform: translateY(100vh) rotate(360deg);
    opacity: 0;
  }
}

.animate-confetti {
  animation: confetti-fall 3s linear infinite;
}
</style>
