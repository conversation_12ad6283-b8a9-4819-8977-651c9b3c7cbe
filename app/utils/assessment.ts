import type {
  Assessmentable,
  AssessmentAiGapFillSentence,
  AssessmentAnswerTheQuestion,
  AssessmentGapFill,
  AssessmentMultiSelect,
  AssessmentWordOrder,
} from '@/types/course'

/**
 * Type guard to check if an assessment is a MultiSelect assessment
 */
export function isMultiSelectAssessment(assessment: Assessmentable): assessment is AssessmentMultiSelect {
  return 'answer_list' in assessment
    && 'correct_answer_indexes' in assessment
    && 'explanations' in assessment
}

/**
 * Type guard to check if an assessment is an AI Gap Fill Sentence assessment
 */
export function isAiGapFillSentenceAssessment(assessment: Assessmentable): assessment is AssessmentAiGapFillSentence {
  return 'context' in assessment
    && 'fill_position' in assessment
}

/**
 * Type guard to check if an assessment is a Gap Fill assessment
 */
export function isGapFillAssessment(assessment: Assessmentable): assessment is AssessmentGapFill {
  return 'correct_answers' in assessment
    && 'explanation' in assessment
    && !('context' in assessment) // Distinguish from AI Gap Fill
    && !('answer_list' in assessment) // Distinguish from MultiSelect
    && 'question' in assessment
    && Object.keys(assessment).length <= 5 // Distinguish from AnswerTheQuestion by field count
}

/**
 * Type guard to check if an assessment is an Answer the Question assessment
 */
export function isAnswerTheQuestionAssessment(assessment: Assessmentable): assessment is AssessmentAnswerTheQuestion {
  return 'correct_answers' in assessment
    && 'explanation' in assessment
    && 'question' in assessment
    && !('context' in assessment) // Distinguish from AI Gap Fill
    && !('answer_list' in assessment) // Distinguish from MultiSelect
    && !('fill_position' in assessment) // Distinguish from AI Gap Fill
    && !('shuffled_words' in assessment) // Distinguish from Word Order
}

/**
 * Type guard to check if an assessment is a Word Order assessment
 */
export function isWordOrderAssessment(assessment: Assessmentable): assessment is AssessmentWordOrder {
  return 'shuffled_words' in assessment
    && !('answer_list' in assessment) // Distinguish from MultiSelect
    && !('context' in assessment) // Distinguish from AI Gap Fill
    && !('correct_answers' in assessment) // Distinguish from other types
}
