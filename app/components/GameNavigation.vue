<script setup lang="ts">
import { ChevronLeft, Heart } from 'lucide-vue-next'

interface Props {
  currentStage: number
  currentLevel: number
  currentQuestionIndex: number
  totalQuestions: number
  score: number
  progressPercentage: number
}

defineProps<Props>()

defineEmits<{
  'go-back': []
}>()
</script>

<template>
  <Card class="mb-6">
    <CardHeader>
      <div class="flex items-center justify-between">
        <Button
          variant="outline"
          size="sm"
          class="flex items-center gap-2"
          @click="$emit('go-back')"
        >
          <ChevronLeft class="w-4 h-4" />
          Back
        </Button>

        <div class="text-center">
          <CardTitle>Stage {{ currentStage }} - Level {{ currentLevel }}</CardTitle>
          <CardDescription>
            Question {{ currentQuestionIndex + 1 }} of {{ totalQuestions }}
          </CardDescription>
        </div>

        <div class="flex items-center gap-2">
          <!-- Score -->
          <div class="text-sm font-medium text-muted-foreground">
            Score: {{ score }}
          </div>
        </div>
      </div>
    </CardHeader>

    <CardContent>
      <!-- Progress Bar -->
      <div class="space-y-2">
        <div class="flex justify-between text-sm text-muted-foreground">
          <span>Progress</span>
          <span>{{ Math.round(progressPercentage) }}%</span>
        </div>
        <Progress
          :model-value="progressPercentage"
          class="h-2"
        />
      </div>
    </CardContent>
  </Card>
</template>
