<script setup lang="ts">
import type { AssessmentMultiSelect } from '@/types/course'
import { Edit, MoreHorizontal, Trash2 } from 'lucide-vue-next'

interface Props {
  assessment: AssessmentMultiSelect
}

interface Emits {
  edit: [assessment: AssessmentMultiSelect]
  delete: [assessment: AssessmentMultiSelect]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

function handleEdit() {
  emit('edit', props.assessment)
}

function handleDelete() {
  emit('delete', props.assessment)
}
</script>

<template>
  <div class="group bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-white dark:hover:bg-gray-800 transition-colors">
    <div class="flex items-start justify-between">
      <div class="flex-1 min-w-0">
        <!-- Assessment Type Badge -->
        <div class="flex items-center gap-2 mb-2">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
            Multiple Select
          </span>
        </div>

        <!-- Question -->
        <h4 class="font-medium text-gray-900 dark:text-white text-sm mb-2 leading-relaxed">
          {{ assessment.question }}
        </h4>

        <!-- Answer indicators -->
        <div class="flex items-center gap-1 mb-2">
          <div
            v-for="(option, index) in assessment.answer_list"
            :key="index"
            class="w-2 h-2 rounded-full"
            :class="{
              'bg-green-500': assessment.correct_answer_indexes.includes(index),
              'bg-gray-300 dark:bg-gray-600': !assessment.correct_answer_indexes.includes(index),
            }"
          />
        </div>

        <!-- Summary -->
        <p class="text-xs text-gray-600 dark:text-gray-400">
          {{ assessment.answer_list.length }} options • {{ assessment.correct_answer_indexes.length }} correct
        </p>

        <!-- Explanation preview -->
        <div v-if="assessment.explanations?.[0]" class="mt-2">
          <p class="text-xs text-gray-500 dark:text-gray-500 italic">
            "{{ assessment.explanations[0].substring(0, 100) }}{{ assessment.explanations[0].length > 100 ? '...' : '' }}"
          </p>
        </div>
      </div>

      <!-- Actions Menu -->
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="ghost" size="sm" class="opacity-0 group-hover:opacity-100 transition-opacity">
            <MoreHorizontal class="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" class="w-40">
          <DropdownMenuItem @click="handleEdit">
            <Edit class="w-4 h-4" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            class="text-red-600 focus:text-red-600 focus:bg-red-50 dark:focus:bg-red-900/20"
            @click="handleDelete"
          >
            <Trash2 class="w-4 h-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  </div>
</template>
