<script setup lang="ts">
import type { AssessmentMultiSelect } from '@/types/course'
import { CheckCircle, ChevronRight, Flag, XCircle } from 'lucide-vue-next'
import { assessmentRepository } from '@/repositories/assessment'

interface QuestionOption {
  text: string
}

interface ProcessedQuestion {
  id: number
  question: string
  options: QuestionOption[]
}

interface Props {
  assessment: AssessmentMultiSelect
  isLastQuestion: boolean
}

interface Emits {
  answerResult: [result: { isCorrect: boolean, score: number }]
  nextQuestion: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Internal component state
const selectedAnswer = ref<number | null>(null)
const answerSelected = ref(false)
const showFeedback = ref(false)
const isCorrect = ref(false)
const loading = ref(false)
const scoringResult = ref<{
  pass: boolean
  explain: string
} | null>(null)

// Reset component state when assessment changes
watch(
  () => props.assessment,
  () => {
    resetComponentState()
  },
  { immediate: true },
)

function resetComponentState() {
  selectedAnswer.value = null
  answerSelected.value = false
  showFeedback.value = false
  isCorrect.value = false
  loading.value = false
  scoringResult.value = null
}

// Convert assessment to question format
const question = computed<ProcessedQuestion>(() => ({
  id: props.assessment.id,
  question: props.assessment.question,
  options: props.assessment.answer_list.map((answer: string) => ({
    text: answer,
  })),
}))

function getOptionClass(index: number) {
  if (!answerSelected.value) {
    return 'bg-background border-border hover:bg-accent hover:text-accent-foreground'
  }

  if (selectedAnswer.value === index) {
    return isCorrect.value
      ? 'bg-green-100 dark:bg-green-900 border-green-500 text-green-800 dark:text-green-200'
      : 'bg-red-100 dark:bg-red-900 border-red-500 text-red-800 dark:text-red-200'
  }

  return 'bg-muted border-border text-muted-foreground'
}

function getOptionBadgeClass(index: number) {
  if (!answerSelected.value) {
    return 'bg-muted text-muted-foreground'
  }

  if (selectedAnswer.value === index) {
    return isCorrect.value
      ? 'bg-green-500 text-white'
      : 'bg-red-500 text-white'
  }

  return 'bg-muted text-muted-foreground'
}

async function handleSelectAnswer(index: number) {
  if (answerSelected.value || loading.value)
    return

  selectedAnswer.value = index
  answerSelected.value = true
  loading.value = true

  try {
    // Score the assessment using the repository
    const result = await assessmentRepository.score(
      props.assessment.id,
      [index], // Send selected answer as array
      'multiple-select',
    )

    scoringResult.value = result
    isCorrect.value = result.pass

    // Use standard score for correct/incorrect (10 for pass, 0 for fail)
    const score = result.pass ? 10 : 0

    // Emit result to parent
    emit('answerResult', { isCorrect: result.pass, score })
  }
  finally {
    loading.value = false
    showFeedback.value = true
  }
}

function handleNextQuestion() {
  emit('nextQuestion')
}
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="text-center text-2xl md:text-3xl">
        {{ question.question }}
      </CardTitle>
    </CardHeader>

    <CardContent>
      <!-- Answer Options -->
      <div class="grid gap-4 md:grid-cols-2 mb-6">
        <button
          v-for="(option, index) in question.options"
          :key="index"
          :disabled="answerSelected || loading"
          :class="getOptionClass(index)"
          class="p-4 rounded-lg text-left font-medium transition-all duration-300 border-2 disabled:cursor-not-allowed hover:scale-[1.02] active:scale-[0.98]"
          @click="handleSelectAnswer(index)"
        >
          <div class="flex items-center gap-3">
            <div
              class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all"
              :class="getOptionBadgeClass(index)"
            >
              {{ String.fromCharCode(65 + index) }}
            </div>
            <span>{{ option.text }}</span>
          </div>
        </button>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="mb-6 text-center">
        <div class="flex items-center justify-center gap-2 text-gray-600 dark:text-gray-400">
          <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600" />
          <span>Checking your answer...</span>
        </div>
      </div>

      <!-- Feedback Message -->
      <div v-if="showFeedback && !loading" class="mb-6">
        <Alert :class="isCorrect ? 'border-green-500 bg-green-50 dark:bg-green-950' : 'border-red-500 bg-red-50 dark:bg-red-950'">
          <div class="flex items-center gap-2">
            <CheckCircle v-if="isCorrect" class="w-5 h-5 text-green-600 dark:text-green-400" />
            <XCircle v-else class="w-5 h-5 text-red-600 dark:text-red-400" />
            <AlertTitle :class="isCorrect ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'">
              {{ isCorrect ? 'Correct! 🎉' : 'Incorrect 😔' }}
            </AlertTitle>
          </div>
          <AlertDescription :class="isCorrect ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'">
            <!-- Use API explanation if available, otherwise use local explanation -->
            <div v-if="scoringResult?.explain">
              {{ scoringResult.explain }}
            </div>
            <div v-else>
              {{ isCorrect ? 'Great job!' : 'Keep trying!' }}
            </div>
          </AlertDescription>
        </Alert>
      </div>

      <!-- Next Button -->
      <div v-if="showFeedback && !loading" class="text-center">
        <Button
          size="lg"
          class="px-8 flex items-center gap-2"
          @click="handleNextQuestion"
        >
          <span>{{ isLastQuestion ? 'Complete Level' : 'Next Question' }}</span>
          <Flag v-if="isLastQuestion" class="w-4 h-4" />
          <ChevronRight v-else class="w-4 h-4" />
        </Button>
      </div>
    </CardContent>
  </Card>
</template>
