<script setup lang="ts">
import type { AssessmentAnswerTheQuestion } from '@/types/course'
import { CheckCircle, ChevronRight, Flag, XCircle } from 'lucide-vue-next'

interface Props {
  assessment: AssessmentAnswerTheQuestion
  isLastQuestion: boolean
}

interface Emits {
  answerResult: [result: { isCorrect: boolean, score: number }]
  nextQuestion: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Internal component state
const userAnswer = ref('')
const answerSubmitted = ref(false)
const showFeedback = ref(false)
const isCorrect = ref(false)
const loading = ref(false)
const validationError = ref<string | null>(null)

// Reset component state when assessment changes
watch(
  () => props.assessment,
  () => {
    resetComponentState()
  },
  { immediate: true },
)

function resetComponentState() {
  userAnswer.value = ''
  answerSubmitted.value = false
  showFeedback.value = false
  isCorrect.value = false
  loading.value = false
  validationError.value = null
}

// Check if answer is filled
const answerFilled = computed(() => {
  return userAnswer.value && userAnswer.value.trim().length > 0
})

function handleAnswerUpdate(value: string) {
  if (answerSubmitted.value)
    return

  // Clear any validation error when user starts typing
  if (validationError.value) {
    validationError.value = null
  }

  userAnswer.value = value
}

function handleSubmitAnswer() {
  if (answerSubmitted.value)
    return

  answerSubmitted.value = true
  loading.value = true

  // Check if answer is filled before submitting
  if (!answerFilled.value) {
    // Show validation error without emitting result
    loading.value = false
    validationError.value = 'Please enter an answer before submitting.'
    answerSubmitted.value = false
    return
  }

  // Simulate loading delay for better UX
  setTimeout(() => {
    // Check answer against correct answers (case-insensitive)
    const userAnswerLower = userAnswer.value.trim().toLowerCase()
    isCorrect.value = props.assessment.correct_answers.some(correctAnswer =>
      correctAnswer.toLowerCase() === userAnswerLower,
    )

    // Calculate score: full score for correct, 0 for incorrect
    const score = isCorrect.value ? 10 : 0

    emit('answerResult', { isCorrect: isCorrect.value, score })

    loading.value = false
    showFeedback.value = true
  }, 1000)
}

function handleNextQuestion() {
  emit('nextQuestion')
}

// Handle Enter key press to submit
function handleKeyPress(event: KeyboardEvent) {
  if (event.key === 'Enter' && answerFilled.value) {
    event.preventDefault()
    handleSubmitAnswer()
  }
}

// Get feedback message
const feedbackMessage = computed(() => {
  if (!showFeedback.value)
    return ''

  if (props.assessment.explanation) {
    return props.assessment.explanation
  }

  if (isCorrect.value) {
    return 'Great job! Your answer is correct.'
  }

  // Show correct answers for incorrect attempts
  const correctAnswersText = props.assessment.correct_answers
    .map(answer => `"${answer}"`)
    .join(', ')
  return `The correct answer${props.assessment.correct_answers.length > 1 ? 's are' : ' is'}: ${correctAnswersText}`
})
</script>

<template>
  <Card>
    <CardContent>
      <!-- Question -->
      <div class="mb-6">
        <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 text-center">
          <h3 class="text-xl md:text-2xl font-medium text-gray-900 dark:text-gray-100 leading-relaxed">
            {{ assessment.question }}
          </h3>
        </div>
      </div>

      <!-- Answer Input -->
      <div class="mb-6">
        <Label for="user-answer" class="text-base font-medium text-gray-900 dark:text-white mb-3 block">
          Your Answer:
        </Label>
        <Textarea
          id="user-answer"
          :model-value="userAnswer"
          :disabled="answerSubmitted"
          :class="{
            'border-green-500 bg-green-50 dark:bg-green-900': showFeedback && isCorrect,
            'border-red-500 bg-red-50 dark:bg-red-900': showFeedback && !isCorrect,
            'border-orange-500 bg-orange-50 dark:bg-orange-900 animate-pulse': validationError,
            'border-gray-300 dark:border-gray-600': !showFeedback && !validationError,
          }"
          class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 text-lg"
          placeholder="Type your answer here..."
          rows="3"
          @update:model-value="handleAnswerUpdate($event as string)"
          @keypress="handleKeyPress"
        />
      </div>

      <!-- Validation Error Message -->
      <div v-if="validationError" class="mb-4">
        <Alert class="border-orange-500 bg-orange-50 dark:bg-orange-950">
          <div class="flex items-center gap-2">
            <XCircle class="w-5 h-5 text-orange-600 dark:text-orange-400" />
            <AlertTitle class="text-orange-800 dark:text-orange-200">
              Missing Information
            </AlertTitle>
          </div>
          <AlertDescription class="text-orange-700 dark:text-orange-300">
            {{ validationError }}
          </AlertDescription>
        </Alert>
      </div>

      <!-- Instructions -->
      <div v-if="!answerSubmitted" class="mb-6 text-center">
        <!-- Submit Button -->
        <Button
          :disabled="!answerFilled || loading"
          size="lg"
          class="px-8"
          @click="handleSubmitAnswer"
        >
          <div v-if="loading" class="flex items-center gap-2">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
            <span>Checking...</span>
          </div>
          <span v-else>Submit Answer</span>
        </Button>
      </div>

      <!-- Feedback Message -->
      <div v-if="showFeedback" class="mb-6">
        <Alert :class="isCorrect ? 'border-green-500 bg-green-50 dark:bg-green-950' : 'border-red-500 bg-red-50 dark:bg-red-950'">
          <div class="flex items-center gap-2">
            <CheckCircle v-if="isCorrect" class="w-5 h-5 text-green-600 dark:text-green-400" />
            <XCircle v-else class="w-5 h-5 text-red-600 dark:text-red-400" />
            <AlertTitle :class="isCorrect ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'">
              {{ isCorrect ? 'Correct! 🎉' : 'Incorrect 😔' }}
            </AlertTitle>
          </div>
          <AlertDescription :class="isCorrect ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'">
            {{ feedbackMessage }}
          </AlertDescription>
        </Alert>
      </div>

      <!-- Next Button -->
      <div v-if="showFeedback" class="text-center">
        <Button
          size="lg"
          class="px-8 flex items-center gap-2"
          @click="handleNextQuestion"
        >
          <span>{{ isLastQuestion ? 'Complete Level' : 'Next Question' }}</span>
          <Flag v-if="isLastQuestion" class="w-4 h-4" />
          <ChevronRight v-else class="w-4 h-4" />
        </Button>
      </div>
    </CardContent>
  </Card>
</template>
