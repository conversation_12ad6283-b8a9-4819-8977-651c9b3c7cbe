<script setup lang="ts">
import type { AssessmentAnswerTheQuestion, CreateAnswerTheQuestionAssessmentData, UpdateAnswerTheQuestionAssessmentData } from '@/types/course'
import { Plus, Save, Trash2 } from 'lucide-vue-next'
import { assessmentRepository } from '@/repositories/assessment'

interface Props {
  open: boolean
  assessment: AssessmentAnswerTheQuestion | null
  unitId?: number
}

interface Emits {
  'update:open': [value: boolean]
  'updated': []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Local state
const isOpen = computed({
  get: () => props.open,
  set: value => emit('update:open', value),
})

const form = ref({
  question: '',
  correct_answers: [''],
  explanation: '',
})

const errors = ref<Record<string, string[]>>({})
const loading = ref(false)

// Computed to determine if we're in create mode
const isCreateMode = computed(() => !props.assessment)

// Initialize form when assessment changes or dialog opens
watch(() => props.open, (open) => {
  if (open) {
    if (props.assessment) {
      // Edit mode
      form.value = {
        question: props.assessment.question,
        correct_answers: [...props.assessment.correct_answers],
        explanation: props.assessment.explanation || '',
      }
    }
    else {
      // Create mode
      form.value = {
        question: '',
        correct_answers: [''],
        explanation: '',
      }
    }
    errors.value = {}
  }
}, { immediate: true })

// Reset form when dialog closes
watch(isOpen, (open) => {
  if (!open) {
    errors.value = {}
  }
})

// Add/remove correct answers
function addCorrectAnswer() {
  form.value.correct_answers.push('')
}

function removeCorrectAnswer(index: number) {
  if (form.value.correct_answers.length > 1) {
    form.value.correct_answers.splice(index, 1)
  }
}

// Form validation
function validateForm(): boolean {
  errors.value = {}

  if (!form.value.question?.trim()) {
    errors.value.question = ['Question is required']
  }

  // Check if all correct answers are filled
  const emptyAnswers = form.value.correct_answers.some(answer => !answer.trim())
  if (emptyAnswers) {
    errors.value.correct_answers = ['All correct answers must be filled']
  }

  // Check if at least one correct answer exists
  if (form.value.correct_answers.length === 0) {
    errors.value.correct_answers = ['At least one correct answer is required']
  }

  return Object.keys(errors.value).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  const { success, error: showError } = useToast()
  loading.value = true

  try {
    if (isCreateMode.value) {
      // Create mode
      if (!props.unitId) {
        errors.value.general = ['Unit ID is required for creating assessments']
        return
      }

      const createData: CreateAnswerTheQuestionAssessmentData & { type: string } = {
        type: 'answer_the_question',
        question: form.value.question,
        correct_answers: form.value.correct_answers,
        explanation: form.value.explanation,
        unit_attachments: [{ unit_id: props.unitId }],
      }

      await assessmentRepository.createAssessment(createData)

      success('Assessment created successfully', {
        description: 'New answer the question assessment has been added to the unit',
        duration: 4000,
      })
    }
    else {
      // Edit mode
      if (!props.assessment) {
        return
      }

      const updateData: UpdateAnswerTheQuestionAssessmentData = {
        question: form.value.question,
        correct_answers: form.value.correct_answers,
        explanation: form.value.explanation,
      }

      await assessmentRepository.updateAssessment('answer_the_question', props.assessment.id, updateData)

      success('Assessment updated successfully', {
        description: 'Your changes have been saved',
        duration: 4000,
      })
    }

    emit('updated')
    isOpen.value = false
  }
  catch (error: any) {
    console.error(`Failed to ${isCreateMode.value ? 'create' : 'update'} assessment:`, error)

    // Handle validation errors from API
    if (error.statusCode === 422 && error.data?.errors) {
      errors.value = error.data.errors
      showError('Validation errors found', {
        description: 'Please check the form and fix any errors',
        duration: 5000,
      })
    }
    else {
      // Use specific error message if available, otherwise fall back to generic message
      const specificError = error.data?.error || error.message || `Failed to ${isCreateMode.value ? 'create' : 'update'} assessment. Please try again.`
      errors.value.general = [specificError]
      showError(`Failed to ${isCreateMode.value ? 'create' : 'update'} assessment`, {
        description: specificError,
        duration: 5000,
      })
    }
  }
  finally {
    loading.value = false
  }
}

function handleCancel() {
  isOpen.value = false
}
</script>

<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="max-w-3xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle class="text-xl font-semibold text-gray-900 dark:text-white">
          {{ isCreateMode ? 'Create Answer the Question Assessment' : 'Edit Answer the Question Assessment' }}
        </DialogTitle>
        <DialogDescription class="text-gray-600 dark:text-gray-400">
          {{ isCreateMode ? 'Create a new answer the question assessment with question and accepted answers.' : 'Update the assessment question, accepted answers, and explanation.' }}
        </DialogDescription>
      </DialogHeader>

      <form class="space-y-6" @submit.prevent="handleSubmit">
        <!-- General Error -->
        <div v-if="errors.general" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div class="text-red-800 dark:text-red-200">
            <ul class="list-disc list-inside space-y-1">
              <li v-for="error in errors.general" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Question -->
        <div class="space-y-2">
          <Label for="question" class="text-gray-900 dark:text-white font-medium">
            Question *
          </Label>
          <Textarea
            id="question"
            v-model="form.question"
            placeholder="Enter the question students need to answer (e.g., 'What is the capital of France?')"
            rows="3"
            :class="{ 'border-red-500': errors.question }"
            class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
            required
          />
          <div v-if="errors.question" class="text-red-600 dark:text-red-400 text-sm">
            <ul class="list-disc list-inside">
              <li v-for="error in errors.question" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Correct Answers -->
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <Label class="text-gray-900 dark:text-white font-medium">
              Accepted Answers *
            </Label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
              @click="addCorrectAnswer"
            >
              <Plus class="w-4 h-4 mr-1" />
              Add Answer
            </Button>
          </div>

          <div class="text-sm text-gray-600 dark:text-gray-400">
            Provide all acceptable answers for this question. Student answers will be matched against these (case-insensitive).
          </div>

          <div class="space-y-3">
            <div
              v-for="(answer, index) in form.correct_answers"
              :key="index"
              class="flex items-center gap-3"
            >
              <!-- Answer Number -->
              <div class="flex-shrink-0 w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ index + 1 }}
                </span>
              </div>

              <!-- Answer Input -->
              <div class="flex-1">
                <Input
                  v-model="form.correct_answers[index]"
                  :placeholder="`Accepted answer ${index + 1}`"
                  :class="{ 'border-red-500': errors.correct_answers }"
                  class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
                  required
                />
              </div>

              <!-- Remove Button -->
              <Button
                v-if="form.correct_answers.length > 1"
                type="button"
                variant="ghost"
                size="sm"
                class="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                @click="removeCorrectAnswer(index)"
              >
                <Trash2 class="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div v-if="errors.correct_answers" class="text-red-600 dark:text-red-400 text-sm">
            <ul class="list-disc list-inside">
              <li v-for="error in errors.correct_answers" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Explanation -->
        <div class="space-y-2">
          <Label for="explanation" class="text-gray-900 dark:text-white font-medium">
            Explanation (Optional)
          </Label>
          <Textarea
            id="explanation"
            v-model="form.explanation"
            placeholder="Provide an explanation for the correct answer"
            rows="2"
            class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
          />
        </div>

        <!-- Dialog Actions -->
        <DialogFooter class="flex items-center gap-3">
          <Button
            type="button"
            variant="outline"
            :disabled="loading"
            class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            @click="handleCancel"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            :disabled="loading"
            class="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Save class="w-4 h-4" />
            {{ loading ? (isCreateMode ? 'Creating...' : 'Saving...') : (isCreateMode ? 'Create Assessment' : 'Save Changes') }}
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>
