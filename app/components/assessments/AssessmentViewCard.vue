<script setup lang="ts">
import type { Assessmentable } from '@/types/course'
import type { FileRecord } from '@/types/storage'
import { getFileExtension } from '#shared/utils/file'
import { useFileDialog } from '@vueuse/core'
import { Music, Upload, X } from 'lucide-vue-next'
import { useModal } from '@/composables/useModal'
import { useToast } from '@/composables/useToast'
import { assessmentRepository } from '@/repositories/assessment'
import { storageRepository } from '@/repositories/storage'
import { isAiGapFillSentenceAssessment, isAnswerTheQuestionAssessment, isGapFillAssessment, isMultiSelectAssessment, isWordOrderAssessment } from '@/utils/assessment'
import AnswerTheQuestionViewCard from './AnswerTheQuestion/AnswerTheQuestionViewCard.vue'
import AssessmentAiGapFillSentenceViewCard from './AssessmentAiGapFillSentence/AssessmentAiGapFillSentenceViewCard.vue'
import GapFillViewCard from './GapFill/GapFillViewCard.vue'
import MultiselectViewCard from './MultiSelect/MultiselectViewCard.vue'
import WordOrderViewCard from './WordOrder/WordOrderViewCard.vue'

interface Props {
  assessment: Assessmentable
}

const props = defineProps<Props>()
const emit = defineEmits<{
  (e: 'edit', assessment: Assessmentable): void
  (e: 'delete', assessment: Assessmentable): void
  (e: 'audioUploaded', assessment: Assessmentable, audioUrl: string): void
  (e: 'fileDeleted', assessment: Assessmentable, fileId: number): void
  (e: 'fileAttached', assessment: Assessmentable, file: FileRecord): void
}>()

const { success, error: showError } = useToast()
const { confirm } = useModal()

// Create a reactive copy of the assessment to manage local state
const localAssessment = ref<Assessmentable>({ ...props.assessment })

// Watch for changes in props.assessment to keep local state in sync
watch(
  () => props.assessment,
  (newAssessment) => {
    localAssessment.value = { ...newAssessment }
  },
  { deep: true },
)

// Computed property to get files with proper display format
const displayFiles = computed(() => {
  if (!localAssessment.value.files || !Array.isArray(localAssessment.value.files)) {
    return []
  }

  return localAssessment.value.files.map((file, index) => {
    // Extract extension from the file path
    const pathParts = file.path.split('.')
    const extension = pathParts.length > 1 ? pathParts.pop() : 'file'
    const displayName = `${index + 1}.${extension}`

    return {
      ...file,
      displayName,
    }
  })
})

// File dialog for audio selection
const { open, reset, onChange } = useFileDialog({
  accept: 'audio/*',
  multiple: false,
})

// Handle file selection from dialog
onChange((selectedFiles) => {
  if (selectedFiles && selectedFiles.length > 0) {
    const file = selectedFiles[0]
    if (file) {
      handleAudioUpload(file)
    }
  }
})

const isUploading = ref(false)

async function handleAudioUpload(file: File) {
  try {
    isUploading.value = true
    const presignedUrl = await storageRepository.generatePresignedTempUploadUrl(getFileExtension(file))
    const isUploaded = await storageRepository.uploadByPresignedUrl(presignedUrl.presigned_url, file)
    if (isUploaded) {
      const fileRecord = await assessmentRepository.attachFileToAssessment(presignedUrl.public_url, props.assessment.assessment_id, 'audio')

      // Update local state with the new file
      if (!localAssessment.value.files) {
        localAssessment.value.files = []
      }
      localAssessment.value.files.push(fileRecord)

      success('Audio file uploaded successfully!')
      emit('audioUploaded', props.assessment, presignedUrl.public_url)
      emit('fileAttached', localAssessment.value, fileRecord)
    }
    else {
      showError('Upload failed, please try again')
    }
  }
  catch (error) {
    showError('Upload failed, please contact support')
    console.error(error)
  }
  finally {
    reset()
    isUploading.value = false
  }
}

// Delete file functionality
async function handleDeleteFile(file: FileRecord & { displayName: string }) {
  const confirmed = await confirm({
    title: 'Delete File',
    description: `Are you sure you want to delete "${file.displayName}"? This action cannot be undone.`,
    variant: 'destructive',
  })

  if (!confirmed) {
    return
  }

  try {
    await assessmentRepository.detachFileFromAssessment(file.id, props.assessment.assessment_id)

    // Update local state by removing the deleted file
    if (localAssessment.value.files) {
      localAssessment.value.files = localAssessment.value.files.filter(f => f.id !== file.id)
    }

    success('File deleted successfully!')
    emit('fileDeleted', localAssessment.value, file.id)
  }
  catch (error) {
    showError('Failed to delete file. Please try again.')
    console.error(error)
  }
}
</script>

<template>
  <div class="relative group">
    <!-- Bottom Right Area - Files Display or Upload Button -->
    <div class="absolute bottom-2 right-2 z-10">
      <!-- Files Display when files exist -->
      <div v-if="displayFiles.length > 0" class="flex flex-col items-end gap-2">
        <div
          v-for="file in displayFiles"
          :key="file.id"
          class="flex items-center gap-2 px-3 py-1.5 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 rounded-lg text-sm shadow-sm"
        >
          <span class="text-gray-700 dark:text-gray-300">{{ file.displayName }}</span>
          <Button
            variant="ghost"
            size="sm"
            class="h-5 w-5 p-0 hover:bg-red-100 dark:hover:bg-red-900/20 hover:text-red-600 dark:hover:text-red-400"
            :aria-label="`Delete ${file.displayName}`"
            :title="`Delete ${file.displayName}`"
            @click="handleDeleteFile(file)"
          >
            <X class="w-3 h-3" />
          </Button>
        </div>
      </div>

      <!-- Upload Button when no files exist -->
      <Button
        v-else
        variant="ghost"
        size="sm"
        class="opacity-100 transition-opacity bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border border-gray-200 dark:border-gray-700 hover:bg-white dark:hover:bg-gray-800 shadow-sm"
        :disabled="isUploading"
        aria-label="Upload audio file for this assessment"
        title="Upload audio file"
        @click="open"
      >
        <Upload v-if="isUploading" class="w-4 h-4 animate-spin" />
        <Music v-else class="w-4 h-4" />
        <span class="sr-only">Upload audio file</span>
      </Button>
    </div>

    <!-- Multiple Select Assessment -->
    <MultiselectViewCard
      v-if="isMultiSelectAssessment(assessment)"
      :assessment="assessment"
      @edit="emit('edit', assessment)"
      @delete="emit('delete', assessment)"
    />

    <!-- AI Gap Fill Sentence Assessment -->
    <AssessmentAiGapFillSentenceViewCard
      v-else-if="isAiGapFillSentenceAssessment(assessment)"
      :assessment="assessment"
      @edit="emit('edit', assessment)"
      @delete="emit('delete', assessment)"
    />

    <!-- Answer the Question Assessment -->
    <AnswerTheQuestionViewCard
      v-else-if="isAnswerTheQuestionAssessment(assessment)"
      :assessment="assessment"
      @edit="emit('edit', assessment)"
      @delete="emit('delete', assessment)"
    />

    <!-- Gap Fill Assessment -->
    <GapFillViewCard
      v-else-if="isGapFillAssessment(assessment)"
      :assessment="assessment"
      @edit="emit('edit', assessment)"
      @delete="emit('delete', assessment)"
    />

    <!-- Word Order Assessment -->
    <WordOrderViewCard
      v-else-if="isWordOrderAssessment(assessment)"
      :assessment="assessment"
      @edit="emit('edit', assessment)"
      @delete="emit('delete', assessment)"
    />

    <!-- Upload Progress Indicator (if needed) -->
    <div
      v-if="isUploading"
      class="absolute inset-0 bg-gray-50/50 dark:bg-gray-900/50 backdrop-blur-sm rounded-lg flex items-center justify-center z-20"
    >
      <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-200 dark:border-gray-700">
        <div class="flex items-center gap-3">
          <Upload class="w-5 h-5 animate-spin text-blue-600" />
          <span class="text-sm font-medium text-gray-900 dark:text-white">
            Uploading audio...
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
