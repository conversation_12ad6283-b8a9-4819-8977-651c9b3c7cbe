<script setup lang="ts">
import type { AssessmentWordOrder } from '@/types/course'
import { CheckCircle, ChevronRight, Flag, RotateCcw, XCircle } from 'lucide-vue-next'
import { assessmentRepository } from '@/repositories/assessment'

interface Props {
  assessment: AssessmentWordOrder
  isLastQuestion: boolean
}

interface Emits {
  answerResult: [result: { isCorrect: boolean, score: number }]
  nextQuestion: []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Internal component state
const selectedWords = ref<string[]>([])
const answerSubmitted = ref(false)
const showFeedback = ref(false)
const isCorrect = ref(false)
const loading = ref(false)
const validationError = ref<string | null>(null)
const scoringResult = ref<any>(null)

// Reset component state when assessment changes
watch(
  () => props.assessment,
  () => {
    resetComponentState()
  },
  { immediate: true },
)

function resetComponentState() {
  selectedWords.value = []
  answerSubmitted.value = false
  showFeedback.value = false
  isCorrect.value = false
  loading.value = false
  validationError.value = null
  scoringResult.value = null
}

// Computed properties
const availableWords = computed(() => {
  return props.assessment.shuffled_words.filter(word => !selectedWords.value.includes(word))
})

const userAnswer = computed(() => {
  return selectedWords.value.join(' ')
})

const answerFilled = computed(() => {
  return selectedWords.value.length > 0
})

// Word selection handlers
function selectWord(word: string) {
  if (answerSubmitted.value)
    return

  if (validationError.value) {
    validationError.value = null
  }

  selectedWords.value.push(word)
}

function removeWord(index: number) {
  if (answerSubmitted.value)
    return
  selectedWords.value.splice(index, 1)
}

function clearAll() {
  if (answerSubmitted.value)
    return
  selectedWords.value = []
}

// Submit answer
async function handleSubmitAnswer() {
  if (answerSubmitted.value)
    return

  answerSubmitted.value = true
  loading.value = true

  // Check if answer is filled before submitting
  if (!answerFilled.value) {
    loading.value = false
    validationError.value = 'Please select at least one word before submitting.'
    answerSubmitted.value = false
    return
  }

  try {
    // Score the assessment using the repository
    const result = await assessmentRepository.score(
      props.assessment,
      [userAnswer.value],
      'word-order',
    )

    scoringResult.value = result
    isCorrect.value = result.pass

    // Calculate score: full score for correct, 0 for incorrect
    const score = isCorrect.value ? 10 : 0

    emit('answerResult', { isCorrect: isCorrect.value, score })

    loading.value = false
    showFeedback.value = true
  }
  catch (error: any) {
    console.error('Failed to score assessment:', error)
    loading.value = false
    validationError.value = 'Failed to submit answer. Please try again.'
    answerSubmitted.value = false
  }
}

function handleNextQuestion() {
  emit('nextQuestion')
}

// Handle Enter key press to submit
function handleKeyPress(event: KeyboardEvent) {
  if (event.key === 'Enter' && answerFilled.value && !answerSubmitted.value) {
    event.preventDefault()
    handleSubmitAnswer()
  }
}

// Add keyboard event listener
onMounted(() => {
  document.addEventListener('keydown', handleKeyPress)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeyPress)
})
</script>

<template>
  <Card>
    <CardContent>
      <!-- Instructions -->
      <div class="mb-6">
        <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center">
          <h3 class="text-lg font-medium text-blue-900 dark:text-blue-100 mb-2">
            Word Ordering Exercise
          </h3>
          <p class="text-blue-700 dark:text-blue-300 text-sm">
            Click the words below to arrange them in the correct order to form a meaningful sentence.
          </p>
        </div>
      </div>

      <!-- Word Pool -->
      <div class="mb-6">
        <Label class="text-base font-medium text-gray-900 dark:text-white mb-3 block">
          Available Words:
        </Label>
        <div class="flex flex-wrap gap-2 min-h-[60px] p-4 bg-gray-50 dark:bg-gray-900 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600">
          <Button
            v-for="word in availableWords"
            :key="word"
            variant="outline"
            size="sm"
            :disabled="answerSubmitted"
            class="border-blue-300 text-blue-700 hover:bg-blue-50 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-900/20"
            @click="selectWord(word)"
          >
            {{ word }}
          </Button>
          <div v-if="availableWords.length === 0" class="text-gray-500 dark:text-gray-400 text-sm italic">
            All words selected
          </div>
        </div>
      </div>

      <!-- Selected Words / Sentence Building Area -->
      <div class="mb-6">
        <div class="flex items-center justify-between mb-3">
          <Label class="text-base font-medium text-gray-900 dark:text-white">
            Your Sentence:
          </Label>
          <Button
            v-if="selectedWords.length > 0 && !answerSubmitted"
            variant="ghost"
            size="sm"
            class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            @click="clearAll"
          >
            <RotateCcw class="w-4 h-4 mr-1" />
            Clear All
          </Button>
        </div>
        <div
          class="min-h-[80px] p-4 bg-white dark:bg-gray-800 rounded-lg border-2 transition-colors"
          :class="{
            'border-green-500 bg-green-50 dark:bg-green-900/20': showFeedback && isCorrect,
            'border-red-500 bg-red-50 dark:bg-red-900/20': showFeedback && !isCorrect,
            'border-orange-500 bg-orange-50 dark:bg-orange-900/20 animate-pulse': validationError,
            'border-gray-300 dark:border-gray-600': !showFeedback && !validationError,
          }"
        >
          <div v-if="selectedWords.length > 0" class="flex flex-wrap gap-2">
            <Button
              v-for="(word, index) in selectedWords"
              :key="`selected-${word}-${index}`"
              variant="secondary"
              size="sm"
              :disabled="answerSubmitted"
              class="bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:hover:bg-blue-800/30"
              @click="removeWord(index)"
            >
              {{ word }}
              <XCircle class="w-3 h-3 ml-1" />
            </Button>
          </div>
          <div v-else class="text-gray-500 dark:text-gray-400 text-sm italic">
            Click words above to build your sentence here...
          </div>
        </div>
      </div>

      <!-- Validation Error -->
      <div v-if="validationError" class="mb-4">
        <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-3">
          <div class="flex items-center">
            <Flag class="w-4 h-4 text-orange-600 dark:text-orange-400 mr-2" />
            <p class="text-orange-800 dark:text-orange-200 text-sm font-medium">
              {{ validationError }}
            </p>
          </div>
        </div>
      </div>

      <!-- Feedback Section -->
      <div v-if="showFeedback && scoringResult" class="mb-6">
        <div
          class="rounded-lg p-4 border-2"
          :class="{
            'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800': isCorrect,
            'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800': !isCorrect,
          }"
        >
          <div class="flex items-center mb-3">
            <CheckCircle v-if="isCorrect" class="w-5 h-5 text-green-600 dark:text-green-400 mr-2" />
            <XCircle v-else class="w-5 h-5 text-red-600 dark:text-red-400 mr-2" />
            <h4
              class="font-semibold" :class="{
                'text-green-800 dark:text-green-200': isCorrect,
                'text-red-800 dark:text-red-200': !isCorrect,
              }"
            >
              {{ isCorrect ? 'Correct!' : 'Incorrect' }}
            </h4>
          </div>

          <!-- Explanation -->
          <div class="mb-3">
            <p
              class="text-sm" :class="{
                'text-green-700 dark:text-green-300': isCorrect,
                'text-red-700 dark:text-red-300': !isCorrect,
              }"
            >
              {{ scoringResult.explain }}
            </p>
          </div>

          <!-- Correct Answer (if incorrect) -->
          <div v-if="!isCorrect && scoringResult.meta?.correct_answer" class="mb-3">
            <p class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Correct Answer:
            </p>
            <p class="text-sm text-gray-600 dark:text-gray-400 bg-white dark:bg-gray-800 p-2 rounded border">
              {{ scoringResult.meta.correct_answer }}
            </p>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <!-- Submit Button -->
          <Button
            v-if="!showFeedback"
            :disabled="!answerFilled || loading"
            class="bg-blue-600 hover:bg-blue-700 text-white"
            @click="handleSubmitAnswer"
          >
            <div v-if="loading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
            {{ loading ? 'Checking...' : 'Submit Answer' }}
          </Button>

          <!-- Next Question Button -->
          <Button
            v-if="showFeedback"
            class="bg-blue-600 hover:bg-blue-700 text-white"
            @click="handleNextQuestion"
          >
            {{ isLastQuestion ? 'Complete' : 'Next Question' }}
            <ChevronRight class="w-4 h-4 ml-1" />
          </Button>
        </div>

        <!-- Word Count Info -->
        <div class="text-sm text-gray-600 dark:text-gray-400">
          {{ selectedWords.length }} / {{ assessment.shuffled_words.length }} words selected
        </div>
      </div>
    </CardContent>
  </Card>
</template>
