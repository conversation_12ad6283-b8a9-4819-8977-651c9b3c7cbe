<script setup lang="ts">
import type { AssessmentWordOrder, CreateWordOrderAssessmentData, UpdateWordOrderAssessmentData } from '@/types/course'
import { Save } from 'lucide-vue-next'
import { assessmentRepository } from '@/repositories/assessment'

interface Props {
  open: boolean
  assessment: AssessmentWordOrder | null
  unitId?: number
}

interface Emits {
  'update:open': [value: boolean]
  'updated': []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Local state
const isOpen = computed({
  get: () => props.open,
  set: value => emit('update:open', value),
})

const form = ref({
  answer: '',
  explanation: '',
})

const errors = ref<Record<string, string[]>>({})
const loading = ref(false)

// Computed to determine if we're in create mode
const isCreateMode = computed(() => !props.assessment)

// Initialize form when assessment changes or dialog opens
watch(() => props.open, (open) => {
  if (open) {
    if (props.assessment) {
      // Edit mode
      form.value = {
        answer: props.assessment.answer || '',
        explanation: props.assessment.explanation || '',
      }
    }
    else {
      // Create mode
      form.value = {
        answer: '',
        explanation: '',
      }
    }
    errors.value = {}
  }
}, { immediate: true })

// Reset form when dialog closes
watch(isOpen, (open) => {
  if (!open) {
    errors.value = {}
  }
})

// Form validation
function validateForm(): boolean {
  errors.value = {}

  if (!form.value.answer?.trim()) {
    errors.value.answer = ['Answer sentence is required']
  }

  if (!form.value.explanation?.trim()) {
    errors.value.explanation = ['Explanation is required']
  }

  return Object.keys(errors.value).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  const { success, error: showError } = useToast()
  loading.value = true

  try {
    if (isCreateMode.value) {
      // Create mode
      if (!props.unitId) {
        showError('Unit ID is required for creating assessments')
        return
      }

      const createData: CreateWordOrderAssessmentData & { type: string } = {
        type: 'word-order',
        question: 'Arrange the words to form a correct sentence', // Default question for word order
        answer: form.value.answer,
        explanation: form.value.explanation,
        unit_attachments: [{ unit_id: props.unitId }],
      }

      await assessmentRepository.createAssessment(createData)

      success('Assessment created successfully', {
        description: 'Your new word order assessment has been added',
        duration: 4000,
      })
    }
    else {
      // Edit mode
      if (!props.assessment) {
        return
      }

      const updateData: UpdateWordOrderAssessmentData = {
        answer: form.value.answer,
        explanation: form.value.explanation,
      }

      await assessmentRepository.updateAssessment('word-order', props.assessment.id, updateData)

      success('Assessment updated successfully', {
        description: 'Your changes have been saved',
        duration: 4000,
      })
    }

    emit('updated')
    isOpen.value = false
  }
  catch (error: any) {
    console.error('Failed to save assessment:', error)

    // Handle validation errors from server
    if (error?.data?.errors) {
      errors.value = error.data.errors
    }
    else {
      showError('Failed to save assessment', {
        description: error.message || 'Please try again later',
        duration: 5000,
      })
    }
  }
  finally {
    loading.value = false
  }
}

function handleCancel() {
  isOpen.value = false
}
</script>

<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="max-w-2xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle class="text-xl font-semibold text-gray-900 dark:text-white">
          {{ isCreateMode ? 'Create Word Order Assessment' : 'Edit Word Order Assessment' }}
        </DialogTitle>
        <DialogDescription class="text-gray-600 dark:text-gray-400">
          {{ isCreateMode ? 'Create a new word ordering exercise with the correct sentence and explanation.' : 'Update the correct sentence and explanation for this word ordering exercise.' }}
        </DialogDescription>
      </DialogHeader>

      <form @submit.prevent="handleSubmit">
        <div class="space-y-6">
          <!-- Answer Field -->
          <div class="space-y-2">
            <Label for="answer" class="text-sm font-medium text-gray-900 dark:text-white">
              Correct Sentence <span class="text-red-500">*</span>
            </Label>
            <Textarea
              id="answer"
              v-model="form.answer"
              :class="{
                'border-red-500 bg-red-50 dark:bg-red-900/20': errors.answer,
                'border-gray-300 dark:border-gray-600': !errors.answer,
              }"
              class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100"
              placeholder="Enter the correct sentence that students should form..."
              rows="3"
              :disabled="loading"
            />
            <p class="text-xs text-gray-600 dark:text-gray-400">
              This sentence will be automatically split into words and shuffled for students to reorder.
            </p>
            <div v-if="errors.answer" class="text-red-600 dark:text-red-400 text-sm">
              <p v-for="error in errors.answer" :key="error">
                {{ error }}
              </p>
            </div>
          </div>

          <!-- Explanation Field -->
          <div class="space-y-2">
            <Label for="explanation" class="text-sm font-medium text-gray-900 dark:text-white">
              Explanation <span class="text-red-500">*</span>
            </Label>
            <Textarea
              id="explanation"
              v-model="form.explanation"
              :class="{
                'border-red-500 bg-red-50 dark:bg-red-900/20': errors.explanation,
                'border-gray-300 dark:border-gray-600': !errors.explanation,
              }"
              class="bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100"
              placeholder="Explain the grammar rule or concept behind this sentence structure..."
              rows="4"
              :disabled="loading"
            />
            <p class="text-xs text-gray-600 dark:text-gray-400">
              This explanation will be shown to students after they submit their answer.
            </p>
            <div v-if="errors.explanation" class="text-red-600 dark:text-red-400 text-sm">
              <p v-for="error in errors.explanation" :key="error">
                {{ error }}
              </p>
            </div>
          </div>

          <!-- Preview Section -->
          <div v-if="form.answer.trim()" class="space-y-2">
            <Label class="text-sm font-medium text-gray-900 dark:text-white">
              Word Preview
            </Label>
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
              <p class="text-xs text-blue-700 dark:text-blue-300 mb-2">
                Students will see these words to arrange:
              </p>
              <div class="flex flex-wrap gap-2">
                <span
                  v-for="word in form.answer.trim().split(/\s+/)"
                  :key="word"
                  class="px-2 py-1 bg-blue-100 dark:bg-blue-800/30 text-blue-800 dark:text-blue-200 text-sm rounded border"
                >
                  {{ word }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Dialog Actions -->
        <DialogFooter class="flex items-center gap-3 mt-6">
          <Button
            type="button"
            variant="outline"
            :disabled="loading"
            class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            @click="handleCancel"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            :disabled="loading"
            class="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Save class="w-4 h-4 mr-2" />
            {{ loading ? (isCreateMode ? 'Creating...' : 'Saving...') : (isCreateMode ? 'Create Assessment' : 'Save Changes') }}
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>
