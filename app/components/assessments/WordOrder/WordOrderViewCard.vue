<script setup lang="ts">
import type { AssessmentWordOrder } from '@/types/course'
import { Edit, MoreHorizontal, Shuffle, Trash2 } from 'lucide-vue-next'

interface Props {
  assessment: AssessmentWordOrder
}

interface Emits {
  edit: [assessment: AssessmentWordOrder]
  delete: [assessment: AssessmentWordOrder]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Computed properties for display
const correctSentence = computed(() => {
  return props.assessment.answer || 'No correct answer set'
})

const wordCount = computed(() => {
  return props.assessment.shuffled_words?.length || 0
})

const previewWords = computed(() => {
  // Show first few words as preview
  const words = props.assessment.shuffled_words || []
  if (words.length <= 4) {
    return words
  }
  return [...words.slice(0, 3), '...']
})

const explanation = computed(() => {
  return props.assessment.explanation || 'No explanation provided'
})

function handleEdit() {
  emit('edit', props.assessment)
}

function handleDelete() {
  emit('delete', props.assessment)
}
</script>

<template>
  <Card class="hover:shadow-md transition-shadow duration-200">
    <CardContent class="p-6">
      <div class="flex items-start justify-between">
        <!-- Main Content -->
        <div class="flex-1 min-w-0">
          <!-- Assessment Type Badge -->
          <div class="flex items-center gap-2 mb-3">
            <Badge variant="secondary" class="bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
              <Shuffle class="w-3 h-3 mr-1" />
              Word Order
            </Badge>
            <span class="text-xs text-gray-500 dark:text-gray-400">
              {{ wordCount }} words
            </span>
          </div>

          <!-- Correct Answer -->
          <div class="mb-4">
            <h3 class="text-sm font-medium text-gray-900 dark:text-white mb-2">
              Correct Sentence:
            </h3>
            <p class="text-gray-700 dark:text-gray-300 text-sm bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg border">
              {{ correctSentence }}
            </p>
          </div>

          <!-- Shuffled Words Preview -->
          <div class="mb-4">
            <h4 class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2 uppercase tracking-wide">
              Word Pool Preview:
            </h4>
            <div class="flex flex-wrap gap-1">
              <span
                v-for="word in previewWords"
                :key="word"
                class="px-2 py-1 bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 text-xs rounded border border-blue-200 dark:border-blue-800"
              >
                {{ word }}
              </span>
            </div>
          </div>

          <!-- Explanation -->
          <div class="mb-4">
            <h4 class="text-xs font-medium text-gray-600 dark:text-gray-400 mb-2 uppercase tracking-wide">
              Explanation:
            </h4>
            <p class="text-gray-600 dark:text-gray-400 text-sm line-clamp-2">
              {{ explanation }}
            </p>
          </div>

          <!-- Metadata -->
          <div class="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
            <span>ID: {{ assessment.id }}</span>
            <span v-if="assessment.created_at">
              Created: {{ new Date(assessment.created_at).toLocaleDateString() }}
            </span>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex items-center gap-2 ml-4">
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="ghost" size="sm" class="h-8 w-8 p-0">
                <MoreHorizontal class="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" class="w-48">
              <DropdownMenuItem @click="handleEdit">
                <Edit class="w-4 h-4 mr-2" />
                Edit Assessment
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                class="text-red-600 dark:text-red-400 focus:text-red-600 dark:focus:text-red-400"
                @click="handleDelete"
              >
                <Trash2 class="w-4 h-4 mr-2" />
                Delete Assessment
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </CardContent>
  </Card>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
