<script setup lang="ts">
import type { AssessmentAiGapFillSentence, AssessmentAnswerTheQuestion, AssessmentContext, AssessmentGapFill, AssessmentMultiSelect, AssessmentWordOrder } from '@/types/course'
import AnswerTheQuestionEditDialog from './AnswerTheQuestion/AnswerTheQuestionEditDialog.vue'
import AssessmentAiGapFillSentenceEditDialog from './AssessmentAiGapFillSentence/AssessmentAiGapFillSentenceEditDialog.vue'
import GapFillEditDialog from './GapFill/GapFillEditDialog.vue'
import MultiselectEditDialog from './MultiSelect/MultiselectEditDialog.vue'
import WordOrderEditDialog from './WordOrder/WordOrderEditDialog.vue'

interface Props {
  assessmentContext: AssessmentContext
}

defineProps<Props>()
defineEmits<{
  (e: 'updated'): void
}>()

const assessmentDialog = defineModel<boolean>('assessmentDialog')
</script>

<template>
  <MultiselectEditDialog
    v-if="assessmentContext.type === 'multiple_select'"
    v-model:open="assessmentDialog as boolean"
    :assessment="assessmentContext.assessment as AssessmentMultiSelect"
    :unit-id="assessmentContext.unitId"
    @updated="$emit('updated')"
  />

  <GapFillEditDialog
    v-else-if="assessmentContext.type === 'gap_fill'"
    v-model:open="assessmentDialog as boolean"
    :assessment="assessmentContext.assessment as AssessmentGapFill"
    :unit-id="assessmentContext.unitId"
    @updated="$emit('updated')"
  />

  <AssessmentAiGapFillSentenceEditDialog
    v-else-if="assessmentContext.type === 'ai_gap_fill_sentence'"
    v-model:open="assessmentDialog as boolean"
    :assessment="assessmentContext.assessment as AssessmentAiGapFillSentence"
    :unit-id="assessmentContext.unitId"
    @updated="$emit('updated')"
  />

  <AnswerTheQuestionEditDialog
    v-else-if="assessmentContext.type === 'answer_the_question'"
    v-model:open="assessmentDialog as boolean"
    :assessment="assessmentContext.assessment as AssessmentAnswerTheQuestion"
    :unit-id="assessmentContext.unitId"
    @updated="$emit('updated')"
  />

  <WordOrderEditDialog
    v-else-if="assessmentContext.type === 'word_order'"
    v-model:open="assessmentDialog as boolean"
    :assessment="assessmentContext.assessment as AssessmentWordOrder"
    :unit-id="assessmentContext.unitId"
    @updated="$emit('updated')"
  />
</template>
