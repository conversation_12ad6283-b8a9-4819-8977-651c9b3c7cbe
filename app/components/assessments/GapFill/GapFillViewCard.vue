<script setup lang="ts">
import type { AssessmentGapFill } from '@/types/course'
import { Edit, MoreHorizontal, Trash2 } from 'lucide-vue-next'

interface Props {
  assessment: AssessmentGapFill
}

interface Emits {
  edit: [assessment: AssessmentGapFill]
  delete: [assessment: AssessmentGapFill]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Count gaps in question
const gapCount = computed(() => {
  const matches = props.assessment.question.match(/_{3,}/g)
  return matches ? matches.length : 0
})

// Preview of question with gaps highlighted
const questionPreview = computed(() => {
  return props.assessment.question.replace(/_{3,}/g, '___')
})

function handleEdit() {
  emit('edit', props.assessment)
}

function handleDelete() {
  emit('delete', props.assessment)
}
</script>

<template>
  <div class="group bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-white dark:hover:bg-gray-800 transition-colors">
    <div class="flex items-start justify-between">
      <div class="flex-1 min-w-0">
        <!-- Assessment Type Badge -->
        <div class="flex items-center gap-2 mb-2">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
            Gap Fill
          </span>
        </div>

        <!-- Question with gaps highlighted -->
        <h4 class="font-medium text-gray-900 dark:text-white text-sm mb-2 leading-relaxed">
          {{ questionPreview }}
        </h4>

        <!-- Correct answers preview -->
        <div class="bg-gray-100 dark:bg-gray-800 rounded-md p-3 mb-2">
          <div class="text-xs text-gray-600 dark:text-gray-400 mb-1">
            Correct Answers:
          </div>
          <div class="flex flex-wrap gap-1">
            <span
              v-for="(answer, index) in assessment.correct_answers"
              :key="index"
              class="inline-flex items-center px-2 py-1 rounded text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
            >
              {{ index + 1 }}. {{ answer }}
            </span>
          </div>
        </div>

        <!-- Summary -->
        <p class="text-xs text-gray-600 dark:text-gray-400">
          {{ gapCount }} gap{{ gapCount !== 1 ? 's' : '' }} • {{ assessment.correct_answers.length }} answer{{ assessment.correct_answers.length !== 1 ? 's' : '' }}
        </p>

        <!-- Explanation preview -->
        <div v-if="assessment.explanation" class="mt-2">
          <p class="text-xs text-gray-500 dark:text-gray-500 italic">
            "{{ assessment.explanation.substring(0, 100) }}{{ assessment.explanation.length > 100 ? '...' : '' }}"
          </p>
        </div>
      </div>

      <!-- Actions Menu -->
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="ghost" size="sm" class="opacity-0 group-hover:opacity-100 transition-opacity">
            <MoreHorizontal class="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" class="w-40">
          <DropdownMenuItem @click="handleEdit">
            <Edit class="w-4 h-4" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            class="text-red-600 focus:text-red-600 focus:bg-red-50 dark:focus:bg-red-900/20"
            @click="handleDelete"
          >
            <Trash2 class="w-4 h-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  </div>
</template>
