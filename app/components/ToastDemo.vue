<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

const { toast, success, error, warning, info, loading, promise, dismiss } = useToast()

// Demo functions for different toast types
function showBasicToast() {
  toast('Hello World!', {
    description: 'This is a basic toast message.',
    duration: 3000,
  })
}

function showSuccessToast() {
  success('Success!', {
    description: 'Your action was completed successfully.',
  })
}

function showErrorToast() {
  error('Error occurred!', {
    description: 'Something went wrong. Please try again.',
  })
}

function showWarningToast() {
  warning('Warning!', {
    description: 'Please check your input and try again.',
  })
}

function showInfoToast() {
  info('Information', {
    description: 'Here is some useful information for you.',
  })
}

function showLoadingToast() {
  const toastId = loading('Loading...', {
    description: 'Please wait while we process your request.',
  })

  // Dismiss after 3 seconds
  setTimeout(() => {
    dismiss(toastId)
    success('Completed!', {
      description: 'The operation finished successfully.',
    })
  }, 3000)
}

function showToastWithAction() {
  toast('Action Required', {
    description: 'Click the button to perform an action.',
    action: {
      label: 'Undo',
      onClick: () => {
        info('Action performed!', {
          description: 'You clicked the action button.',
        })
      },
    },
  })
}

function showPromiseToast() {
  const mockPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
      if (Math.random() > 0.5) {
        resolve('Success data')
      }
      else {
        reject(new Error('Something went wrong'))
      }
    }, 2000)
  })

  promise(mockPromise, {
    loading: 'Processing...',
    success: data => `Success: ${data}`,
    error: error => `Error: ${error.message}`,
  })
}

// Game-specific toast examples
function showGameToasts() {
  // Example: Level completion
  success('Level Complete!', {
    description: 'You earned 100 points!',
    duration: 4000,
  })

  setTimeout(() => {
    // Example: Achievement unlocked
    info('Achievement Unlocked!', {
      description: '🏆 First Level Master',
      duration: 5000,
    })
  }, 1000)
}

function showAssessmentToasts() {
  // Example: Assessment saved
  success('Assessment Saved', {
    description: 'Your progress has been saved automatically.',
  })

  setTimeout(() => {
    // Example: Time warning
    warning('Time Running Out!', {
      description: 'You have 2 minutes remaining.',
      duration: 6000,
    })
  }, 1500)
}
</script>

<template>
  <Card class="w-full max-w-2xl mx-auto">
    <CardHeader>
      <CardTitle>Toast Notifications Demo</CardTitle>
      <CardDescription>
        Try different types of toast notifications using vue-sonner
      </CardDescription>
    </CardHeader>
    <CardContent class="space-y-4">
      <div class="grid grid-cols-2 gap-3">
        <Button variant="outline" @click="showBasicToast">
          Basic Toast
        </Button>

        <Button variant="outline" class="text-green-600" @click="showSuccessToast">
          Success Toast
        </Button>

        <Button variant="outline" class="text-red-600" @click="showErrorToast">
          Error Toast
        </Button>

        <Button variant="outline" class="text-yellow-600" @click="showWarningToast">
          Warning Toast
        </Button>

        <Button variant="outline" class="text-blue-600" @click="showInfoToast">
          Info Toast
        </Button>

        <Button variant="outline" @click="showLoadingToast">
          Loading Toast
        </Button>

        <Button variant="outline" @click="showToastWithAction">
          Toast with Action
        </Button>

        <Button variant="outline" @click="showPromiseToast">
          Promise Toast
        </Button>
      </div>

      <div class="border-t pt-4">
        <h3 class="text-lg font-semibold mb-3">
          Game-Specific Examples
        </h3>
        <div class="grid grid-cols-2 gap-3">
          <Button variant="default" @click="showGameToasts">
            Game Progress Toasts
          </Button>

          <Button variant="default" @click="showAssessmentToasts">
            Assessment Toasts
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
