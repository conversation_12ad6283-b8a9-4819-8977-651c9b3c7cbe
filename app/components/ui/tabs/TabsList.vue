<script setup lang="ts">
import type { TabsListProps } from 'reka-ui'
import type { HTMLAttributes } from 'vue'
import { reactiveOmit } from '@vueuse/core'
import { TabsList } from 'reka-ui'
import { cn } from '@/lib/utils'

const props = defineProps<TabsListProps & { class?: HTMLAttributes['class'] }>()

const delegatedProps = reactiveOmit(props, 'class')
</script>

<template>
  <TabsList
    data-slot="tabs-list"
    v-bind="delegatedProps"
    :class="cn(
      'bg-gray-100 dark:bg-gray-800 text-muted-foreground inline-flex h-11 w-fit items-center justify-center rounded-lg p-1 border border-gray-200 dark:border-gray-700',
      props.class,
    )"
  >
    <slot />
  </TabsList>
</template>
