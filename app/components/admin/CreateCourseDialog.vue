<script setup lang="ts">
import type { CreateCourseData } from '@/types/course'
import { Plus } from 'lucide-vue-next'
import { courseRepository } from '@/repositories/course'

// Props
interface Props {
  open: boolean
}

// Emits
interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'created', course: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Form state
const form = ref<CreateCourseData>({
  title: '',
  description: '',
})
const errors = ref<Record<string, string[]>>({})
const loading = ref(false)

// Form validation
function validateForm(): boolean {
  errors.value = {}

  if (!form.value.title.trim()) {
    errors.value.title = ['Course title is required']
  }

  if (!form.value.description.trim()) {
    errors.value.description = ['Course description is required']
  }

  return Object.keys(errors.value).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    const course = await courseRepository.createCourse(form.value)

    // Close dialog and reset form
    emit('update:open', false)
    resetForm()

    // Emit created event
    emit('created', course)

    // Show success toast
    console.log('Course created successfully:', course)
  }
  catch (error: any) {
    console.error('Failed to create course:', error)

    // Handle validation errors from API
    if (error.statusCode === 422 && error.data?.errors) {
      errors.value = error.data.errors
    }
    else {
      // Show generic error toast
      errors.value.general = ['Failed to create course. Please try again.']
    }
  }
  finally {
    loading.value = false
  }
}

// Reset form
function resetForm() {
  form.value = { title: '', description: '' }
  errors.value = {}
}

// Close dialog
function closeDialog() {
  emit('update:open', false)
}

// Reset form when dialog closes
watch(() => props.open, (isOpen) => {
  if (!isOpen) {
    resetForm()
  }
})
</script>

<template>
  <Dialog :open="open" @update:open="emit('update:open', $event)">
    <DialogContent class="sm:max-w-[500px]">
      <DialogHeader>
        <DialogTitle>Create Course</DialogTitle>
        <DialogDescription>
          Add a new learning course to the system
        </DialogDescription>
      </DialogHeader>

      <form class="space-y-6" @submit.prevent="handleSubmit">
        <!-- General Error -->
        <div v-if="errors.general" class="bg-red-50 border border-red-200 rounded-md p-4">
          <div class="text-red-800">
            <ul class="list-disc list-inside space-y-1">
              <li v-for="error in errors.general" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Course Title -->
        <div class="space-y-2">
          <Label for="create-title">Course Title *</Label>
          <Input
            id="create-title"
            v-model="form.title"
            placeholder="Enter course title"
            :class="{ 'border-red-500': errors.title }"
            required
          />
          <div v-if="errors.title" class="text-red-600 text-sm">
            <ul class="list-disc list-inside">
              <li v-for="error in errors.title" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Course Description -->
        <div class="space-y-2">
          <Label for="create-description">Course Description *</Label>
          <Textarea
            id="create-description"
            v-model="form.description"
            placeholder="Enter course description"
            rows="4"
            :class="{ 'border-red-500': errors.description }"
            required
          />
          <div v-if="errors.description" class="text-red-600 text-sm">
            <ul class="list-disc list-inside">
              <li v-for="error in errors.description" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Form Actions -->
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            :disabled="loading"
            @click="closeDialog"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            :disabled="loading"
            class="min-w-[120px]"
          >
            <div v-if="loading" class="flex items-center gap-2">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
              <span>Creating...</span>
            </div>
            <div v-else class="flex items-center gap-2">
              <Plus class="w-4 h-4" />
              <span>Create Course</span>
            </div>
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>
