<script setup lang="ts">
import type { CreateUnitData, Difficulty, SkillType } from '@/types/course'
import { difficultyOptions, skillTypeOptions } from '#shared/constants'
import { Plus } from 'lucide-vue-next'
import { getAssessmentTypeOptions } from '@/helpers/assessment'
import { unitRepository } from '@/repositories/unit'

// Props
interface Props {
  open: boolean
  defaultCourseId?: number
}

// Emits
interface Emits {
  (e: 'update:open', value: boolean): void
  (e: 'created', unit: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const assessmentTypeOptions = await getAssessmentTypeOptions()

// Form state
const form = ref<CreateUnitData>({
  course_id: 0,
  title: '',
  description: '',
  skill_type: 'vocabulary',
  difficulty: 'beginner',
  unit_order: 1,
  unit_type: assessmentTypeOptions[0]?.value || '',
})
const errors = ref<Record<string, string[]>>({})
const loading = ref(false)

// Fetch courses for dropdown
const { data: coursesData } = useAsyncData(
  'coursesForCreate',
  () => {
    const { call } = useAPICall()
    return call('/api/admin/courses', {
      method: 'GET',
      query: { per_page: 100 },
    })
  },
)

const courses = computed(() => coursesData.value?.data || [])

// Form validation
function validateForm(): boolean {
  errors.value = {}

  if (!form.value.title.trim()) {
    errors.value.title = ['Unit title is required']
  }

  if (!form.value.description.trim()) {
    errors.value.description = ['Unit description is required']
  }

  if (!form.value.course_id || form.value.course_id === 0) {
    errors.value.course_id = ['Course is required']
  }

  if (form.value.unit_order < 1) {
    errors.value.unit_order = ['Unit order must be at least 1']
  }

  return Object.keys(errors.value).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    const unit = await unitRepository.createUnit(form.value)

    // Close dialog and reset form
    emit('update:open', false)
    resetForm()

    // Emit created event
    emit('created', unit)

    // Show success toast
    console.log('Unit created successfully:', unit)
  }
  catch (error: any) {
    console.error('Failed to create unit:', error)

    // Handle validation errors from API
    if (error.statusCode === 422 && error.data?.errors) {
      errors.value = error.data.errors
    }
    else {
      // Show generic error toast
      errors.value.general = ['Failed to create unit. Please try again.']
    }
  }
  finally {
    loading.value = false
  }
}

// Reset form
function resetForm() {
  form.value = {
    course_id: props.defaultCourseId || 0,
    title: '',
    description: '',
    skill_type: 'vocabulary',
    difficulty: 'beginner',
    unit_order: 1,
    unit_type: assessmentTypeOptions[0]?.value || '',
  }
  errors.value = {}
}

// Close dialog
function closeDialog() {
  emit('update:open', false)
}

// Reset form when dialog opens/closes and set course if provided
watch(() => props.open, (isOpen) => {
  if (isOpen) {
    // When dialog opens, set the course if defaultCourseId is provided
    if (props.defaultCourseId) {
      form.value.course_id = props.defaultCourseId
    }
  }
  else {
    // When dialog closes, reset form
    resetForm()
  }
})

// Watch for changes in defaultCourseId and update form
watch(() => props.defaultCourseId, (newCourseId) => {
  if (newCourseId && props.open) {
    form.value.course_id = newCourseId
  }
})
</script>

<template>
  <Dialog :open="open" @update:open="emit('update:open', $event)">
    <DialogContent class="sm:max-w-[600px]">
      <DialogHeader>
        <DialogTitle>Create Unit</DialogTitle>
        <DialogDescription>
          Add a new learning unit to a course
        </DialogDescription>
      </DialogHeader>

      <form class="space-y-6" @submit.prevent="handleSubmit">
        <!-- General Error -->
        <div v-if="errors.general" class="bg-red-50 border border-red-200 rounded-md p-4">
          <div class="text-red-800">
            <ul class="list-disc list-inside space-y-1">
              <li v-for="error in errors.general" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Course Selection -->
          <div class="space-y-2 md:col-span-2">
            <Label for="create-course">Course *</Label>
            <Select
              :model-value="form.course_id.toString()"
              @update:model-value="form.course_id = Number($event)"
            >
              <SelectTrigger
                :class="{ 'border-red-500': errors.course_id }"
                class="w-full"
              >
                <SelectValue placeholder="Select a course" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="course in courses"
                  :key="course.id"
                  :value="course.id.toString()"
                >
                  {{ course.title }}
                </SelectItem>
              </SelectContent>
            </Select>
            <div v-if="errors.course_id" class="text-red-600 text-sm">
              <ul class="list-disc list-inside">
                <li v-for="error in errors.course_id" :key="error">
                  {{ error }}
                </li>
              </ul>
            </div>
          </div>

          <!-- Unit Title -->
          <div class="space-y-2 md:col-span-2">
            <Label for="create-title">Unit Title *</Label>
            <Input
              id="create-title"
              v-model="form.title"
              placeholder="Enter unit title"
              :class="{ 'border-red-500': errors.title }"
              required
            />
            <div v-if="errors.title" class="text-red-600 text-sm">
              <ul class="list-disc list-inside">
                <li v-for="error in errors.title" :key="error">
                  {{ error }}
                </li>
              </ul>
            </div>
          </div>

          <!-- Skill Type -->
          <div class="space-y-2">
            <Label for="create-skill-type">Skill Type *</Label>
            <Select
              :model-value="form.skill_type"
              @update:model-value="form.skill_type = $event as SkillType"
            >
              <SelectTrigger class="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="option in skillTypeOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- Difficulty -->
          <div class="space-y-2">
            <Label for="create-difficulty">Difficulty *</Label>
            <Select
              :model-value="form.difficulty"
              @update:model-value="form.difficulty = $event as Difficulty"
            >
              <SelectTrigger class="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="option in difficultyOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- Assessment Type -->
          <div class="space-y-2">
            <Label for="create-assessment-type">Assessment Type *</Label>
            <Select
              :model-value="form.unit_type"
              @update:model-value="form.unit_type = $event as string"
            >
              <SelectTrigger class="w-full">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem
                  v-for="option in assessmentTypeOptions"
                  :key="option.value"
                  :value="option.value"
                >
                  {{ option.name }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- Unit Order -->
          <div class="space-y-2">
            <Label for="create-order">Unit Order *</Label>
            <Input
              id="create-order"
              v-model.number="form.unit_order"
              type="number"
              min="1"
              placeholder="1"
              :class="{ 'border-red-500': errors.unit_order }"
              required
            />
            <div v-if="errors.unit_order" class="text-red-600 text-sm">
              <ul class="list-disc list-inside">
                <li v-for="error in errors.unit_order" :key="error">
                  {{ error }}
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Unit Description -->
        <div class="space-y-2">
          <Label for="create-description">Unit Description *</Label>
          <Textarea
            id="create-description"
            v-model="form.description"
            placeholder="Enter unit description"
            rows="4"
            :class="{ 'border-red-500': errors.description }"
            required
          />
          <div v-if="errors.description" class="text-red-600 text-sm">
            <ul class="list-disc list-inside">
              <li v-for="error in errors.description" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Form Actions -->
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            :disabled="loading"
            @click="closeDialog"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            :disabled="loading"
            class="min-w-[120px]"
          >
            <div v-if="loading" class="flex items-center gap-2">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-current" />
              <span>Creating...</span>
            </div>
            <div v-else class="flex items-center gap-2">
              <Plus class="w-4 h-4" />
              <span>Create Unit</span>
            </div>
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>
