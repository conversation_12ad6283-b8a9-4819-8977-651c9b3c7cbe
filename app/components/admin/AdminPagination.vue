<script setup lang="ts">
interface PaginationData {
  current_page: number
  last_page: number
  per_page: number
  total: number
  from: number
  to: number
}

interface Props {
  pagination: PaginationData
  itemName?: string
}

const props = withDefaults(defineProps<Props>(), {
  itemName: 'items',
})

const emit = defineEmits<{
  'prev-page': []
  'next-page': []
}>()

const showPagination = computed(() => {
  return props.pagination.total > props.pagination.per_page
})
</script>

<template>
  <AdminCard v-if="showPagination" padding="sm">
    <div class="flex items-center justify-between">
      <p class="text-sm font-medium text-gray-700 dark:text-gray-300">
        Showing
        <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.from }}</span>
        to
        <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.to }}</span>
        of
        <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.total }}</span>
        {{ itemName }}
      </p>

      <div class="flex items-center gap-3">
        <Button
          variant="outline"
          size="sm"
          :disabled="pagination.current_page <= 1"
          class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
          @click="emit('prev-page')"
        >
          Previous
        </Button>

        <span class="text-sm font-medium text-gray-700 dark:text-gray-300 px-3">
          Page
          <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.current_page }}</span>
          of
          <span class="font-semibold text-gray-900 dark:text-white">{{ pagination.last_page }}</span>
        </span>

        <Button
          variant="outline"
          size="sm"
          :disabled="pagination.current_page >= pagination.last_page"
          class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50"
          @click="emit('next-page')"
        >
          Next
        </Button>
      </div>
    </div>
  </AdminCard>
</template>
