<script setup lang="ts">
import type { Assessment, CreateAssessmentData } from '@/types/course'
import { Plus, Save, Trash2 } from 'lucide-vue-next'
import { assessmentRepository } from '@/repositories/assessment'

interface Props {
  open: boolean
  assessment: Assessment | null
  unitId?: number
}

interface Emits {
  'update:open': [value: boolean]
  'updated': []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Local state
const isOpen = computed({
  get: () => props.open,
  set: value => emit('update:open', value),
})

const form = ref({
  question: '',
  answer_list: ['', ''],
  correct_answer_indexes: [] as number[],
  explanations: [''],
})

const errors = ref<Record<string, string[]>>({})
const loading = ref(false)

// Computed to determine if we're in create mode
const isCreateMode = computed(() => !props.assessment)

// Initialize form when assessment changes or dialog opens
watch(() => props.open, (open) => {
  if (open) {
    if (props.assessment) {
      // Edit mode
      form.value = {
        question: props.assessment.question,
        answer_list: [...props.assessment.answer_list],
        correct_answer_indexes: [...props.assessment.correct_answer_indexes],
        explanations: props.assessment.explanations ? [...props.assessment.explanations] : [''],
      }
    }
    else {
      // Create mode
      form.value = {
        question: '',
        answer_list: ['', ''],
        correct_answer_indexes: [],
        explanations: [''],
      }
    }
    errors.value = {}
  }
}, { immediate: true })

// Reset form when dialog closes
watch(isOpen, (open) => {
  if (!open) {
    errors.value = {}
  }
})

// Add/remove answer options
function addAnswerOption() {
  form.value.answer_list.push('')
}

function removeAnswerOption(index: number) {
  if (form.value.answer_list.length > 2) {
    form.value.answer_list.splice(index, 1)
    // Remove from correct answers if it was selected
    form.value.correct_answer_indexes = form.value.correct_answer_indexes
      .filter(i => i !== index)
      .map(i => i > index ? i - 1 : i)
  }
}

// Toggle correct answer
function toggleCorrectAnswer(index: number, checked: boolean | 'indeterminate') {
  const isChecked = checked === true
  if (isChecked) {
    if (!form.value.correct_answer_indexes.includes(index)) {
      form.value.correct_answer_indexes.push(index)
    }
  }
  else {
    const currentIndex = form.value.correct_answer_indexes.indexOf(index)
    if (currentIndex > -1) {
      form.value.correct_answer_indexes.splice(currentIndex, 1)
    }
  }
}

// Check if answer is correct
function isAnswerCorrect(index: number): boolean {
  return form.value.correct_answer_indexes.includes(index)
}

// Form validation
function validateForm(): boolean {
  errors.value = {}

  if (!form.value.question?.trim()) {
    errors.value.question = ['Question is required']
  }

  // Check if all answer options are filled
  const emptyAnswers = form.value.answer_list.some(answer => !answer.trim())
  if (emptyAnswers) {
    errors.value.answer_list = ['All answer options must be filled']
  }

  // Check if at least one correct answer is selected
  if (form.value.correct_answer_indexes.length === 0) {
    errors.value.correct_answer_indexes = ['At least one correct answer must be selected']
  }

  return Object.keys(errors.value).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  const { success, error: showError } = useToast()
  loading.value = true

  try {
    if (isCreateMode.value) {
      // Create mode
      if (!props.unitId) {
        errors.value.general = ['Unit ID is required for creating assessments']
        return
      }

      const createData: CreateAssessmentData = {
        question: form.value.question,
        answer_list: form.value.answer_list,
        correct_answer_indexes: form.value.correct_answer_indexes,
        explanations: form.value.explanations,
        unit_attachments: [{ unit_id: props.unitId }],
      }

      await assessmentRepository.createAssessment(createData)

      success('Assessment created successfully', {
        description: 'New assessment has been added to the unit',
        duration: 4000,
      })
    }
    else {
      // Edit mode
      if (!props.assessment) {
        return
      }
      await assessmentRepository.updateAssessment(props.assessment.id, {
        question: form.value.question,
        answer_list: form.value.answer_list,
        correct_answer_indexes: form.value.correct_answer_indexes,
        explanations: form.value.explanations,
      })

      success('Assessment updated successfully', {
        description: 'Your changes have been saved',
        duration: 4000,
      })
    }

    emit('updated')
    isOpen.value = false
  }
  catch (error: any) {
    console.error(`Failed to ${isCreateMode.value ? 'create' : 'update'} assessment:`, error)

    // Handle validation errors from API
    if (error.statusCode === 422 && error.data?.errors) {
      errors.value = error.data.errors
      showError('Validation errors found', {
        description: 'Please check the form and fix any errors',
        duration: 5000,
      })
    }
    else {
      errors.value.general = [`Failed to ${isCreateMode.value ? 'create' : 'update'} assessment. Please try again.`]
      showError(`Failed to ${isCreateMode.value ? 'create' : 'update'} assessment`, {
        description: error.message || 'Please try again later',
        duration: 5000,
      })
    }
  }
  finally {
    loading.value = false
  }
}

function handleCancel() {
  isOpen.value = false
}
</script>

<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="max-w-2xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle class="text-xl font-semibold text-gray-900 dark:text-white">
          {{ isCreateMode ? 'Create Assessment' : 'Edit Assessment' }}
        </DialogTitle>
        <DialogDescription class="text-gray-600 dark:text-gray-400">
          {{ isCreateMode ? 'Create a new assessment with question, answer options, and explanations.' : 'Update the assessment question, answer options, and explanations.' }}
        </DialogDescription>
      </DialogHeader>

      <form class="space-y-6" @submit.prevent="handleSubmit">
        <!-- General Error -->
        <div v-if="errors.general" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div class="text-red-800 dark:text-red-200">
            <ul class="list-disc list-inside space-y-1">
              <li v-for="error in errors.general" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Question -->
        <div class="space-y-2">
          <Label for="question" class="text-gray-900 dark:text-white font-medium">
            Question *
          </Label>
          <Textarea
            id="question"
            v-model="form.question"
            placeholder="Enter the assessment question"
            rows="3"
            :class="{ 'border-red-500': errors.question }"
            class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
            required
          />
          <div v-if="errors.question" class="text-red-600 dark:text-red-400 text-sm">
            <ul class="list-disc list-inside">
              <li v-for="error in errors.question" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Answer Options -->
        <div class="space-y-3">
          <div class="flex items-center justify-between">
            <Label class="text-gray-900 dark:text-white font-medium">
              Answer Options *
            </Label>
            <Button
              type="button"
              variant="outline"
              size="sm"
              class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
              @click="addAnswerOption"
            >
              <Plus class="w-4 h-4 mr-1" />
              Add Option
            </Button>
          </div>

          <div class="space-y-3">
            <div
              v-for="(answer, index) in form.answer_list"
              :key="index"
              class="flex items-center gap-3"
            >
              <!-- Option Letter -->
              <div class="flex-shrink-0 w-8 h-8 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {{ String.fromCharCode(65 + index) }}
                </span>
              </div>

              <!-- Answer Input -->
              <div class="flex-1">
                <Input
                  v-model="form.answer_list[index]"
                  :placeholder="`Option ${String.fromCharCode(65 + index)}`"
                  :class="{ 'border-red-500': errors.answer_list }"
                  class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
                  required
                />
              </div>

              <!-- Correct Answer Checkbox -->
              <div class="flex items-center gap-2">
                <Checkbox
                  :id="`correct-${index}`"
                  :model-value="isAnswerCorrect(index)"
                  @update:model-value="(checked) => toggleCorrectAnswer(index, checked)"
                />
                <Label :for="`correct-${index}`" class="text-sm text-gray-700 dark:text-gray-300">
                  Correct
                </Label>
              </div>

              <!-- Remove Button -->
              <Button
                v-if="form.answer_list.length > 2"
                type="button"
                variant="ghost"
                size="sm"
                class="text-red-600 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20"
                @click="removeAnswerOption(index)"
              >
                <Trash2 class="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div v-if="errors.answer_list" class="text-red-600 dark:text-red-400 text-sm">
            <ul class="list-disc list-inside">
              <li v-for="error in errors.answer_list" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>

          <div v-if="errors.correct_answer_indexes" class="text-red-600 dark:text-red-400 text-sm">
            <ul class="list-disc list-inside">
              <li v-for="error in errors.correct_answer_indexes" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Explanation -->
        <div class="space-y-2">
          <Label for="explanation" class="text-gray-900 dark:text-white font-medium">
            Explanation (Optional)
          </Label>
          <Textarea
            id="explanation"
            v-model="form.explanations[0]"
            placeholder="Provide an explanation for the correct answer"
            rows="2"
            class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
          />
        </div>

        <!-- Dialog Actions -->
        <DialogFooter class="flex items-center gap-3">
          <Button
            type="button"
            variant="outline"
            :disabled="loading"
            class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            @click="handleCancel"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            :disabled="loading"
            class="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Save class="w-4 h-4" />
            {{ loading ? (isCreateMode ? 'Creating...' : 'Saving...') : (isCreateMode ? 'Create Assessment' : 'Save Changes') }}
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>
