<script setup lang="ts">
</script>

<script lang="ts">
import { Plus } from 'lucide-vue-next'

interface Props {
  title: string
  description?: string
  actionLabel?: string
  actionTo?: string
  actionClick?: () => void
  showAction?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  description: '',
  actionLabel: 'Create',
  showAction: true,
})
</script>

<template>
  <div class="flex items-center justify-between">
    <div>
      <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
        {{ title }}
      </h1>
      <p v-if="description" class="text-gray-700 dark:text-gray-300 mt-2 font-medium">
        {{ description }}
      </p>
    </div>

    <div v-if="showAction" class="flex items-center gap-3">
      <slot name="actions">
        <Button
          v-if="actionTo"
          as-child
          class="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
        >
          <NuxtLink :to="actionTo">
            <Plus class="w-4 h-4" />
            {{ actionLabel }}
          </NuxtLink>
        </Button>

        <Button
          v-else-if="actionClick"
          class="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
          @click="actionClick"
        >
          <Plus class="w-4 h-4" />
          {{ actionLabel }}
        </Button>
      </slot>
    </div>
  </div>
</template>
