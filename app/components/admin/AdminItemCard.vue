<script setup lang="ts">
interface Props {
  title: string
  description?: string
  metadata?: Array<{ label: string, value: string | number }>
  badges?: Array<{ text: string, variant?: 'primary' | 'secondary' | 'outline' | 'success' | 'warning' | 'danger' }>
  showActions?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  description: '',
  metadata: () => [],
  badges: () => [],
  showActions: true,
  loading: false,
})

const badgeVariantClasses = {
  primary: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300',
  secondary: 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300',
  outline: 'border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300',
  success: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300',
  warning: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300',
  danger: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300',
}
</script>

<template>
  <div class="group bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-5 hover:bg-white dark:hover:bg-gray-800 hover:shadow-md transition-all duration-200">
    <div class="flex items-start justify-between">
      <div class="flex-1 min-w-0">
        <!-- Title and Badges -->
        <div class="flex items-center gap-3 mb-3">
          <h3 class="font-semibold text-gray-900 dark:text-white text-lg truncate">
            {{ title }}
          </h3>
          <div v-if="badges.length > 0" class="flex items-center gap-2">
            <Badge
              v-for="(badge, index) in badges"
              :key="index"
              :variant="badge.variant === 'outline' ? 'outline' : 'secondary'"
              :class="badgeVariantClasses[badge.variant || 'secondary']"
              class="font-medium"
            >
              {{ badge.text }}
            </Badge>
          </div>
        </div>

        <!-- Description -->
        <p v-if="description" class="text-gray-700 dark:text-gray-300 mb-3 leading-relaxed">
          {{ description }}
        </p>

        <!-- Metadata -->
        <div v-if="metadata.length > 0" class="flex items-center gap-6 text-sm text-gray-600 dark:text-gray-400">
          <span
            v-for="(item, index) in metadata"
            :key="index"
            class="flex items-center gap-1"
          >
            <span class="font-medium">{{ item.label }}:</span>
            {{ item.value }}
          </span>
        </div>

        <!-- Custom content slot -->
        <div v-if="$slots.content" class="mt-3">
          <slot name="content" />
        </div>
      </div>

      <!-- Actions -->
      <div v-if="showActions && $slots.actions" class="ml-4">
        <div class="opacity-0 group-hover:opacity-100 transition-opacity">
          <slot name="actions" />
        </div>
      </div>
    </div>

    <!-- Footer slot -->
    <div v-if="$slots.footer" class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
      <slot name="footer" />
    </div>
  </div>
</template>
