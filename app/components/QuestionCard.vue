<script setup lang="ts">
import { CheckCircle, ChevronRight, Flag, XCircle } from 'lucide-vue-next'

interface QuestionOption {
  text: string
  isCorrect: boolean
}

interface Question {
  id: number
  question: string
  options: QuestionOption[]
  explanation?: string
  image?: string
}

interface Props {
  question: Question
  selectedAnswer: number | null
  answerSelected: boolean
  showFeedback: boolean
  isCorrect: boolean
  isLastQuestion: boolean
}

const props = defineProps<Props>()

defineEmits<{
  'select-answer': [index: number]
  'next-question': []
}>()

function getOptionClass(index: number) {
  if (!props.answerSelected) {
    return 'bg-background border-border hover:bg-accent hover:text-accent-foreground'
  }

  if (props.selectedAnswer === index) {
    return props.isCorrect
      ? 'bg-green-100 dark:bg-green-900 border-green-500 text-green-800 dark:text-green-200'
      : 'bg-red-100 dark:bg-red-900 border-red-500 text-red-800 dark:text-red-200'
  }

  if (props.question.options[index]?.isCorrect) {
    return 'bg-green-100 dark:bg-green-900 border-green-500 text-green-800 dark:text-green-200'
  }

  return 'bg-muted border-border text-muted-foreground'
}

function getOptionBadgeClass(index: number) {
  if (!props.answerSelected) {
    return 'bg-muted text-muted-foreground'
  }

  if (props.selectedAnswer === index) {
    return props.isCorrect
      ? 'bg-green-500 text-white'
      : 'bg-red-500 text-white'
  }

  if (props.question.options[index]?.isCorrect) {
    return 'bg-green-500 text-white'
  }

  return 'bg-muted text-muted-foreground'
}
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="text-center text-2xl md:text-3xl">
        {{ question.question }}
      </CardTitle>
      <div v-if="question.image" class="flex justify-center mt-6">
        <img
          :src="question.image"
          :alt="question.question"
          class="max-w-sm rounded-lg shadow-md"
        >
      </div>
    </CardHeader>

    <CardContent>
      <!-- Answer Options -->
      <div class="grid gap-4 md:grid-cols-2 mb-6">
        <button
          v-for="(option, index) in question.options"
          :key="index"
          :disabled="answerSelected"
          :class="getOptionClass(index)"
          class="p-4 rounded-lg text-left font-medium transition-all duration-300 border-2 disabled:cursor-not-allowed hover:scale-[1.02] active:scale-[0.98]"
          @click="$emit('select-answer', index)"
        >
          <div class="flex items-center gap-3">
            <div
              class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all"
              :class="getOptionBadgeClass(index)"
            >
              {{ String.fromCharCode(65 + index) }}
            </div>
            <span>{{ option.text }}</span>
          </div>
        </button>
      </div>

      <!-- Feedback Message -->
      <div v-if="showFeedback" class="mb-6">
        <Alert :class="isCorrect ? 'border-green-500 bg-green-50 dark:bg-green-950' : 'border-red-500 bg-red-50 dark:bg-red-950'">
          <div class="flex items-center gap-2">
            <CheckCircle v-if="isCorrect" class="w-5 h-5 text-green-600 dark:text-green-400" />
            <XCircle v-else class="w-5 h-5 text-red-600 dark:text-red-400" />
            <AlertTitle :class="isCorrect ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'">
              {{ isCorrect ? 'Correct! 🎉' : 'Incorrect 😔' }}
            </AlertTitle>
          </div>
          <AlertDescription v-if="question.explanation" :class="isCorrect ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'">
            {{ question.explanation }}
          </AlertDescription>
        </Alert>
      </div>

      <!-- Next Button -->
      <div v-if="showFeedback" class="text-center">
        <Button
          size="lg"
          class="px-8 flex items-center gap-2"
          @click="$emit('next-question')"
        >
          <span>{{ isLastQuestion ? 'Complete Level' : 'Next Question' }}</span>
          <Flag v-if="isLastQuestion" class="w-4 h-4" />
          <ChevronRight v-else class="w-4 h-4" />
        </Button>
      </div>
    </CardContent>
  </Card>
</template>
