<script setup lang="ts">
import { Heart, Home, RotateCcw, X } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'

interface Props {
  score?: number
  correctAnswers?: number
  totalQuestions?: number
  stage?: number
  level?: number
}

withDefaults(defineProps<Props>(), {
  score: 0,
  correctAnswers: 0,
  totalQuestions: 10,
  stage: 1,
  level: 1,
})

const emit = defineEmits<{
  resolve: [action: 'tryAgain' | 'mainMenu']
  close: []
}>()

function handleTryAgain() {
  emit('resolve', 'tryAgain')
}

function handleMainMenu() {
  emit('resolve', 'mainMenu')
}
</script>

<template>
  <div class="text-center space-y-6">
    <!-- Game Over Animation -->
    <div class="relative">
      <div class="w-20 h-20 mx-auto bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-4">
        <Heart class="w-10 h-10 text-red-500 animate-pulse" />
      </div>
      <div class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
        <X class="w-4 h-4 text-white" />
      </div>
    </div>

    <!-- Game Statistics -->
    <div class="space-y-4">
      <div class="grid grid-cols-2 gap-4 text-center">
        <div class="bg-muted/50 rounded-lg p-3">
          <div class="text-2xl font-bold text-blue-600">
            {{ score }}
          </div>
          <div class="text-sm text-muted-foreground">
            Final Score
          </div>
        </div>
        <div class="bg-muted/50 rounded-lg p-3">
          <div class="text-2xl font-bold text-green-600">
            {{ correctAnswers }}
          </div>
          <div class="text-sm text-muted-foreground">
            Correct Answers
          </div>
        </div>
      </div>

      <div class="bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg p-4">
        <div class="text-sm font-medium text-orange-800 dark:text-orange-200">
          💡 Tip: Review the questions you missed and try again!
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-3 pt-4">
      <Button
        class="flex-1 flex items-center justify-center gap-2"
        size="lg"
        @click="handleTryAgain"
      >
        <RotateCcw class="w-4 h-4" />
        Try Again
      </Button>
      <Button
        variant="outline"
        class="flex-1 flex items-center justify-center gap-2"
        size="lg"
        @click="handleMainMenu"
      >
        <Home class="w-4 h-4" />
        Main Menu
      </Button>
    </div>
  </div>
</template>
