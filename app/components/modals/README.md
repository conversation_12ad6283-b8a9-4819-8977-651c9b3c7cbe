# Modal Dialog System

A comprehensive modal dialog system built with Shadcn UI components and Vue 3 Composition API for the English Learning Game application.

## Features

- 🎯 **Easy Import**: Simply import the `useModal()` composable where needed
- 🎨 **Multiple Variants**: Default, destructive, success, warning, and info styles
- 📱 **Responsive Design**: Works seamlessly on desktop and mobile devices
- ⌨️ **Keyboard Navigation**: Full keyboard support with ESC and Enter keys
- 🔒 **Persistent Modals**: Optional persistent modals that can't be dismissed accidentally
- 🎭 **Custom Components**: Support for custom Vue components within modals
- 📦 **TypeScript Support**: Full type safety with comprehensive interfaces
- 🎪 **Preset Configurations**: Pre-built modal configurations for common use cases

## Quick Start

### Basic Usage

```vue
<script setup lang="ts">
// Nuxt auto-imports useModal composable
const modal = useModal()

// Simple alert
async function showAlert() {
  await modal.alert({
    title: 'Success!',
    description: 'Your changes have been saved.',
    variant: 'success'
  })
}

// Confirmation dialog
async function deleteItem() {
  const confirmed = await modal.confirm({
    title: 'Delete Item',
    description: 'Are you sure you want to delete this item? This action cannot be undone.',
    variant: 'destructive'
  })

  if (confirmed) {
    // Proceed with deletion
  }
}
</script>
```

### Custom Component Modal

```vue
<script setup lang="ts">
async function showCustomModal() {
  const result = await modal.custom(MyCustomComponent, {
    // Props for the custom component
    userId: 123,
    initialData: { name: 'John' }
  }, {
    // Modal configuration
    title: 'Edit User',
    size: 'lg'
  })

  console.log('Modal result:', result)
}
</script>
```

## API Reference

### `useModal()` Composable

Returns an object with the following properties and methods:

#### State Properties

- `isOpen`: Readonly ref indicating if a modal is currently open
- `config`: Readonly ref containing the current modal configuration
- `loading`: Readonly ref indicating if the modal is in a loading state

#### Methods

##### `open(config: ModalConfig)`

Opens a modal with the provided configuration.

##### `close(): Promise<void>`

Closes the currently open modal.

##### `confirm(config): Promise<boolean>`

Shows a confirmation dialog. Returns `true` if confirmed, `false` if cancelled.

##### `alert(config): Promise<void>`

Shows an alert dialog. Returns when the alert is dismissed.

##### `custom(component, props?, config?): Promise<any>`

Shows a custom component in a modal. Returns the value emitted by the component.

##### `isModalOpen(id?: string): boolean`

Checks if a modal is currently open, optionally by ID.

##### `setLoading(loading: boolean)`

Sets the loading state of the modal.

### Configuration Options

```typescript
interface ModalConfig {
  id?: string // Unique identifier
  title?: string // Modal title
  description?: string // Modal description
  content?: string // HTML content (if not using custom component)
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full' // Modal size
  variant?: 'default' | 'destructive' | 'success' | 'warning' | 'info'
  showCloseButton?: boolean // Show/hide close button (default: true)
  persistent?: boolean // Prevent closing by clicking outside or ESC
  buttons?: ModalButton[] // Custom buttons
  onClose?: () => void | Promise<void> // Callback when modal closes
  customComponent?: any // Vue component to render
  customProps?: Record<string, any> // Props for custom component
}
```

### Button Configuration

```typescript
interface ModalButton {
  text: string // Button text
  variant?: ButtonVariant // Button style variant
  size?: ButtonSize // Button size
  action?: () => void | Promise<void> // Click handler
  loading?: boolean // Show loading spinner
  disabled?: boolean // Disable button
  icon?: string // Icon name (from Lucide)
}
```

## Modal Variants

### Default

Standard modal appearance with neutral colors.

### Destructive

Red-themed modal for dangerous actions like deletions.

### Success

Green-themed modal for positive feedback.

### Warning

Yellow-themed modal for cautionary messages.

### Info

Blue-themed modal for informational content.

## Modal Sizes

- **sm**: Small modal (max-width: 384px)
- **md**: Medium modal (max-width: 448px) - default
- **lg**: Large modal (max-width: 512px)
- **xl**: Extra large modal (max-width: 576px)
- **full**: Full-screen modal

## Preset Configurations

The `modalPresets` object provides common configurations:

```typescript
// Nuxt auto-imports modalPresets
const { modalPresets } = useModal()

// Delete confirmation
const confirmed = await modal.confirm(modalPresets.deleteConfirm('user'))

// Success message
await modal.alert(modalPresets.success('User created successfully!'))

// Error message
await modal.alert(modalPresets.error('Failed to save changes'))

// Game-specific presets
await modal.alert(modalPresets.gameOver())
await modal.alert(modalPresets.levelComplete(2, 3))
```

## Custom Components

When creating custom components for modals, emit events to communicate with the modal system:

```vue
<!-- CustomModalComponent.vue -->
<script setup lang="ts">
const emit = defineEmits<{
  resolve: [value: any] // Resolves the modal promise
  close: [] // Closes the modal without value
}>()

function handleSave() {
  emit('resolve', { action: 'save', data: 'some data' })
}

function handleCancel() {
  emit('close')
}
</script>

<template>
  <div>
    <p>Custom modal content</p>
    <div class="flex gap-2 mt-4">
      <Button @click="handleSave">
        Save
      </Button>
      <Button variant="outline" @click="handleCancel">
        Cancel
      </Button>
    </div>
  </div>
</template>
```

## Examples in the Codebase

### Game Over Modal

- Location: `@/components/modals/GameOverModal.vue`
- Usage: Displayed when player runs out of lives
- Features: Score display, retry/main menu options

### Level Complete Modal

- Location: `@/components/modals/LevelCompleteModal.vue`
- Usage: Shown when player completes a level
- Features: Performance stats, achievements, progression options

### Index Page Examples

- Confirmation before starting new game
- Alert when no progress exists for continue game

### Game Page Integration

- Game over modal with custom styling and actions
- Level completion modal with achievements and progression

## Best Practices

1. **Use TypeScript**: Always type your modal configurations for better development experience
2. **Handle Promises**: Always handle the returned promises from modal methods
3. **Persistent Modals**: Use persistent modals for critical actions that shouldn't be accidentally dismissed
4. **Loading States**: Use the loading functionality for async operations
5. **Accessibility**: Modals automatically handle focus management and keyboard navigation
6. **Error Handling**: Wrap modal actions in try-catch blocks for robust error handling

## Troubleshooting

### Modal Not Appearing

- Ensure `GlobalModal` component is included in your app layout (`app.vue`)
- Verify you're using the auto-imported composable correctly

### TypeScript Errors

- Types are auto-imported with the composable
- Ensure your custom components emit the correct events
- Check that your configuration objects match the expected interfaces

### Styling Issues

- Verify Shadcn components are properly installed
- Check that Tailwind CSS is configured correctly
- Ensure design tokens are available in your CSS
