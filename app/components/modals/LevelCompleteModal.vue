<script setup lang="ts">
import { <PERSON><PERSON><PERSON>, CheckCircle, RotateCcw, Star, Trophy } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'

interface Props {
  stage: number
  level: number
  score: number
  correctAnswers: number
  totalQuestions: number
  isLastLevel?: boolean
  achievements?: string[]
}

const props = withDefaults(defineProps<Props>(), {
  isLastLevel: false,
  achievements: () => [],
})

const emit = defineEmits<{
  resolve: [action: 'next' | 'replay' | 'mainMenu']
  close: []
}>()

const accuracy = computed(() => {
  return Math.round((props.correctAnswers / props.totalQuestions) * 100)
})

const starsEarned = computed(() => {
  if (accuracy.value >= 90)
    return 3
  if (accuracy.value >= 75)
    return 2
  if (accuracy.value >= 60)
    return 1
  return 0
})

function handleNext() {
  emit('resolve', 'next')
}

function handleReplay() {
  emit('resolve', 'replay')
}
</script>

<template>
  <div class="text-center space-y-6">
    <!-- Success Animation -->
    <div class="relative">
      <div class="w-20 h-20 mx-auto bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mb-4 animate-pulse">
        <Trophy class="w-10 h-10 text-white" />
      </div>
      <div class="absolute -top-1 -right-1 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center animate-bounce">
        <CheckCircle class="w-5 h-5 text-white" />
      </div>
    </div>

    <!-- Celebration Message -->
    <div class="space-y-2">
      <h3 class="text-xl font-bold text-primary">
        Level Complete! 🎉
      </h3>
      <p class="text-muted-foreground">
        Stage {{ stage }}, Level {{ level }}
      </p>
    </div>

    <!-- Performance Stats -->
    <div class="grid grid-cols-3 gap-3 text-center">
      <div class="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
        <div class="text-xl font-bold text-blue-600">
          {{ score }}
        </div>
        <div class="text-xs text-muted-foreground">
          Score
        </div>
      </div>
      <div class="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
        <div class="text-xl font-bold text-green-600">
          {{ accuracy }}%
        </div>
        <div class="text-xs text-muted-foreground">
          Accuracy
        </div>
      </div>
      <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-3">
        <div class="text-xl font-bold text-purple-600">
          {{ starsEarned }}/3
        </div>
        <div class="text-xs text-muted-foreground">
          Stars
        </div>
      </div>
    </div>

    <!-- Stars Display -->
    <div class="flex justify-center gap-2">
      <Star
        v-for="star in 3"
        :key="star"
        :class="star <= starsEarned ? 'text-yellow-400 fill-current' : 'text-gray-300'"
        class="w-8 h-8"
      />
    </div>

    <!-- Bonus Messages -->
    <div v-if="achievements.length > 0" class="space-y-2">
      <div class="text-sm font-medium text-primary">
        New Achievements!
      </div>
      <div class="flex flex-wrap justify-center gap-2">
        <span
          v-for="achievement in achievements"
          :key="achievement"
          class="text-xs px-2 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 rounded-full"
        >
          {{ achievement }}
        </span>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-col sm:flex-row gap-3 pt-4">
      <Button
        class="flex-1 flex items-center justify-center gap-2"
        size="lg"
        @click="handleNext"
      >
        <ArrowRight class="w-4 h-4" />
        {{ isLastLevel ? 'Next Stage' : 'Next Level' }}
      </Button>
      <Button
        variant="outline"
        class="flex items-center justify-center gap-2"
        size="lg"
        @click="handleReplay"
      >
        <RotateCcw class="w-4 h-4" />
        Replay
      </Button>
    </div>
  </div>
</template>
