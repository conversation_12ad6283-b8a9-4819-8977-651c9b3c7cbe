<script setup lang="ts">
import type { ModalButton } from '@/types/modal'
import { AlertCircle, AlertTriangle, CheckCircle, Gift, Heart, Info, Loader2, Pause, Play, Settings, Star, Trophy, User, X } from 'lucide-vue-next'
import { resolveModal, useModal } from '@/composables/useModal'

const modal = useModal()

// Size classes for different modal sizes
function getSizeClass(size?: string) {
  const sizeClasses = {
    sm: 'sm:max-w-sm',
    md: 'sm:max-w-md',
    lg: 'sm:max-w-lg',
    xl: 'sm:max-w-xl',
    full: 'sm:max-w-screen-lg',
  }
  return sizeClasses[size as keyof typeof sizeClasses] || sizeClasses.md
}

// Variant styling classes
function getVariantClass(variant?: string) {
  const variantClasses = {
    default: '',
    destructive: 'border-destructive/50',
    success: 'border-green-500/50',
    warning: 'border-yellow-500/50',
    info: 'border-blue-500/50',
  }
  return variantClasses[variant as keyof typeof variantClasses] || variantClasses.default
}

// Variant icons
function getVariantIcon(variant?: string) {
  const variantIcons = {
    default: null,
    destructive: AlertTriangle,
    success: CheckCircle,
    warning: AlertCircle,
    info: Info,
  }
  return variantIcons[variant as keyof typeof variantIcons]
}

// Variant icon classes
function getVariantIconClass(variant?: string) {
  const iconClasses = {
    default: '',
    destructive: 'text-destructive',
    success: 'text-green-500',
    warning: 'text-yellow-500',
    info: 'text-blue-500',
  }
  return iconClasses[variant as keyof typeof iconClasses] || iconClasses.default
}

// Variant description classes
function getVariantDescriptionClass(variant?: string) {
  const descriptionClasses = {
    default: 'text-muted-foreground',
    destructive: 'text-destructive/90',
    success: 'text-green-600 dark:text-green-400',
    warning: 'text-yellow-600 dark:text-yellow-400',
    info: 'text-blue-600 dark:text-blue-400',
  }
  return descriptionClasses[variant as keyof typeof descriptionClasses] || descriptionClasses.default
}

// Button icon mapping
function getButtonIcon(iconName?: string) {
  const iconMap: Record<string, any> = {
    play: Play,
    pause: Pause,
    settings: Settings,
    user: User,
    heart: Heart,
    star: Star,
    trophy: Trophy,
    gift: Gift,
    close: X,
    check: CheckCircle,
    warning: AlertTriangle,
    info: Info,
  }
  return iconName ? iconMap[iconName] : null
}

// Handle dialog open/close state changes
function handleOpenChange(open: boolean) {
  if (!open) {
    modal.close()
  }
}

// Handle clicks outside the modal
function handleOutsideClick(event: Event) {
  if (modal.config.value?.persistent) {
    event.preventDefault()
    return
  }
  modal.close()
}

// Handle escape key
function handleEscapeKey(event: KeyboardEvent) {
  if (modal.config.value?.persistent) {
    event.preventDefault()
    return
  }
  modal.close()
}

// Handle button clicks
async function handleButtonClick(button: ModalButton) {
  if (button.disabled || modal.loading.value)
    return

  try {
    if (button.action) {
      // Set loading state if this is an async action
      modal.setLoading(true)
      await button.action()
      modal.setLoading(false)
    }
  }
  catch (error) {
    modal.setLoading(false)
    console.error('Error in button action:', error)
  }
}

// Handle custom component resolution
function handleCustomResolve(value: any) {
  resolveModal(value)
}

// Keyboard navigation
function handleKeydown(event: KeyboardEvent) {
  if (!modal.isOpen.value)
    return

  // Handle Enter key for default action
  if (event.key === 'Enter' && modal.config.value?.buttons) {
    const defaultButton = modal.config.value.buttons.find(b => b.variant === 'default')
      || modal.config.value.buttons[modal.config.value.buttons.length - 1]
    if (defaultButton && !defaultButton.disabled) {
      event.preventDefault()
      handleButtonClick(defaultButton)
    }
  }
}

// Set up keyboard listeners
if (process.client) {
  document.addEventListener('keydown', handleKeydown)
}

// Cleanup on unmount
onUnmounted(() => {
  if (process.client) {
    document.removeEventListener('keydown', handleKeydown)
  }
})
</script>

<template>
  <Dialog
    :open="modal.isOpen.value"
    @update:open="handleOpenChange"
  >
    <DialogContent
      class="sm:max-w-md" :class="[
        getSizeClass(modal.config.value?.size),
        getVariantClass(modal.config.value?.variant),
      ]"
      :show-close="modal.config.value?.showCloseButton !== false"
      @pointer-down-outside="handleOutsideClick"
      @escape-key-down="handleEscapeKey"
    >
      <!-- Modal Header -->
      <DialogHeader>
        <DialogTitle
          class="flex items-center gap-2" :class="[
            !modal.config.value?.title && 'sr-only',
          ]"
        >
          <component
            :is="getVariantIcon(modal.config.value?.variant)"
            v-if="getVariantIcon(modal.config.value?.variant) && modal.config.value?.title"
            :class="getVariantIconClass(modal.config.value?.variant)"
            class="w-5 h-5"
          />
          {{ modal.config.value?.title || 'Modal' }}
        </DialogTitle>
        <DialogDescription
          :class="[
            getVariantDescriptionClass(modal.config.value?.variant),
            !modal.config.value?.description && 'sr-only',
          ]"
        >
          {{ modal.config.value?.description || 'Modal content' }}
        </DialogDescription>
      </DialogHeader>

      <!-- Modal Content -->
      <div class="py-4">
        <!-- Custom Component -->
        <component
          :is="modal.config.value.customComponent"
          v-if="modal.config.value?.customComponent"
          v-bind="modal.config.value?.customProps || {}"
          @close="modal.close"
          @resolve="handleCustomResolve"
        />

        <!-- Simple Text Content -->
        <div
          v-else-if="modal.config.value?.content"
          class="text-sm text-muted-foreground"
          v-html="modal.config.value.content"
        />
      </div>

      <!-- Modal Footer with Buttons -->
      <DialogFooter
        v-if="modal.config.value?.buttons && modal.config.value.buttons.length > 0"
        class="flex-col-reverse sm:flex-row"
      >
        <Button
          v-for="(button, index) in modal.config.value.buttons"
          :key="index"
          :variant="button.variant || 'default'"
          :size="button.size || 'default'"
          :disabled="button.disabled || modal.loading.value"
          class="flex items-center gap-2"
          @click="handleButtonClick(button)"
        >
          <component
            :is="getButtonIcon(button.icon)"
            v-if="button.icon && !button.loading"
            class="w-4 h-4"
          />
          <Loader2
            v-if="button.loading"
            class="w-4 h-4 animate-spin"
          />
          {{ button.text }}
        </Button>
      </DialogFooter>

      <!-- Loading Overlay -->
      <div
        v-if="modal.loading.value"
        class="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center rounded-lg"
      >
        <div class="flex items-center gap-2 text-muted-foreground">
          <Loader2 class="w-4 h-4 animate-spin" />
          <span class="text-sm">Loading...</span>
        </div>
      </div>
    </DialogContent>
  </Dialog>
</template>
