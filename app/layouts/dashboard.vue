<script setup lang="ts">
// This layout is used for authenticated pages with sidebar navigation
const { headerConfig } = usePageHeader()

// Staff Authentication (only shown when enabled)
const { isAuthenticated, staff } = useStaffAuth()
const { logoutWithState, loading, reset } = useStaffAuthState()
const router = useRouter()

// Methods
async function handleLogout() {
  try {
    reset()
    await logoutWithState()
    router.push('/login')
  }
  catch (err) {
    console.error('Logout failed:', err)
  }
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <!-- Reusable Header -->
        <ClientOnly>
          <header class="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
            <div class="flex items-center justify-between w-full px-4">
              <div class="flex items-center gap-2">
                <SidebarTrigger class="-ml-1" />
                <Separator
                  orientation="vertical"
                  class="mr-2 data-[orientation=vertical]:h-4"
                />
                <Breadcrumb>
                  <BreadcrumbList>
                    <template v-for="(item, index) in (headerConfig.breadcrumbs || [])" :key="index">
                      <BreadcrumbItem v-if="!item.active" class="hidden md:block">
                        <BreadcrumbLink :href="item.href || '#'">
                          {{ item.label }}
                        </BreadcrumbLink>
                      </BreadcrumbItem>
                      <BreadcrumbItem v-else>
                        <BreadcrumbPage>{{ item.label }}</BreadcrumbPage>
                      </BreadcrumbItem>
                      <BreadcrumbSeparator v-if="index < (headerConfig.breadcrumbs || []).length - 1" class="hidden md:block" />
                    </template>
                  </BreadcrumbList>
                </Breadcrumb>
              </div>

              <!-- Staff Authentication Status (conditional) -->
              <div v-if="headerConfig.showAuthStatus" class="flex items-center gap-3">
                <div v-if="isAuthenticated && staff" class="flex items-center gap-3">
                  <div class="text-sm">
                    <span class="text-gray-600">Welcome,</span>
                    <span class="font-medium ml-1">{{ staff.username }}</span>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    :disabled="loading"
                    @click="handleLogout"
                  >
                    <div v-if="loading" class="flex items-center gap-2">
                      <div class="animate-spin rounded-full h-3 w-3 border-b-2 border-current" />
                      <span>Logging out...</span>
                    </div>
                    <span v-else>Logout</span>
                  </Button>
                </div>
                <div v-else class="flex items-center gap-3">
                  <span class="text-sm text-gray-500">Not authenticated</span>
                  <Button variant="default" size="sm" @click="$router.push('/login')">
                    Login
                  </Button>
                </div>
              </div>
            </div>
          </header>
        </ClientOnly>

        <main class="flex-1">
          <slot />
        </main>
      </SidebarInset>
    </SidebarProvider>
  </div>
</template>
